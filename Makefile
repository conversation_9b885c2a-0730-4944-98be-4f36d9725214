# DigWis Panel Makefile
# 现代化 Go + HTMX + Templ + Tailwind 开发工具

# 🔧 项目配置 - 自动识别路径
APP_NAME ?= digwis-panel
PROJECT_ROOT := $(shell pwd)
PROJECT_NAME := $(shell basename $(PROJECT_ROOT))
AIR_PATH = ./tools/air
BIN_DIR = ./bin
TMP_DIR = ./tmp
TOOLS_DIR = ./tools

.PHONY: help clean install test dev air run-local build release-build deploy-local rollback

# 默认目标 - 显示帮助
help:
	@echo "🚀 DigWis Panel 开发工具"
	@echo "========================"
	@echo ""
	@echo "🔥 开发环境 (推荐):"
	@echo "  make dev           - 启动 Air 热重载开发服务器"
	@echo "  make air           - 启动 Air (别名)"
	@echo "  make run-local     - 快速启动 (无热重载)"
	@echo ""
	@echo "🛠️  构建工具:"
	@echo "  make templ-watch   - 监听模板文件变化"
	@echo "  make templ-build   - 生成模板文件"
	@echo "  make css-watch     - 监听 CSS 文件变化"
	@echo "  make css-build     - 构建 CSS 文件"
	@echo ""
	@echo "📦 构建发布:"
	@echo "  make build         - 构建生产版本"
	@echo "  make release-build - 构建发布包"
	@echo ""
	@echo "🔧 工具管理:"
	@echo "  make install       - 安装所有依赖"
	@echo "  make install-air   - 安装 Air 到项目本地"
	@echo "  make clean         - 清理构建文件"
	@echo "  make test          - 运行测试"
	@echo ""
	@echo "⚡ 快捷命令:"
	@echo "  make start         - 启动开发服务器 (dev 别名)"
	@echo "  make serve         - 启动开发服务器 (dev 别名)"
	@echo "  make watch         - 启动开发服务器 (dev 别名)"

# 🔥 主要开发命令
# Air 热重载开发服务器 (推荐)
dev: install-air
	@echo "🔥 启动 $(PROJECT_NAME) Air 开发服务器"
	@echo "====================================="
	@echo "📂 项目: $(PROJECT_NAME)"
	@echo "📍 路径: $(PROJECT_ROOT)"
	@echo "🔧 Air: $(AIR_PATH)"
	@echo "🌐 地址: http://localhost:9090"
	@echo "====================================="
	@$(AIR_PATH)

# Air 别名 - 更简单的命令
air: dev

# 模板文件监听
templ-watch:
	@echo "👀 监听模板文件变化..."
	@if [ -f "$$HOME/local/go/bin/go" ]; then \
		PATH=$$HOME/local/go/bin:$$PATH GOPROXY=https://goproxy.cn,direct $$HOME/local/go/bin/go run github.com/a-h/templ/cmd/templ@latest generate --watch; \
	else \
		PATH=$$HOME/local/go/bin:$$PATH GOPROXY=https://goproxy.cn,direct go run github.com/a-h/templ/cmd/templ@latest generate --watch; \
	fi

# 模板文件生成
templ-build:
	@echo "🔨 生成模板文件..."
	@if [ -f "$$HOME/local/go/bin/go" ]; then \
		PATH=$$HOME/local/go/bin:$$PATH GOPROXY=https://goproxy.cn,direct $$HOME/local/go/bin/go run github.com/a-h/templ/cmd/templ@latest generate; \
	else \
		PATH=$$HOME/local/go/bin:$$PATH GOPROXY=https://goproxy.cn,direct go run github.com/a-h/templ/cmd/templ@latest generate; \
	fi

# CSS 文件监听
css-watch:
	@echo "🎨 监听 CSS 文件变化..."
	@npm run watch-css

# CSS 文件构建
css-build:
	@echo "🎨 构建 CSS 文件..."
	@npm run build-css-prod

# 安装所有依赖
install: install-air
	@echo "📦 安装 npm 依赖..."
	@npm install
	@echo "✅ 所有依赖安装完成"



# 构建生产版本
build: templ-build css-build
	@echo "📦 构建生产版本..."
	@if [ -f "$$HOME/local/go/bin/go" ]; then \
		CGO_ENABLED=1 $$HOME/local/go/bin/go build -ldflags "-s -w" -o ./bin/$(APP_NAME) .; \
	else \
		CGO_ENABLED=1 go build -ldflags "-s -w" -o ./bin/$(APP_NAME) .; \
	fi
	@echo "✅ 构建完成: ./bin/$(APP_NAME)"

# 构建发布包
release-build:
	@echo "📦 构建发布包..."
	@chmod +x build_release.sh
	@./build_release.sh
	@echo "✅ 发布包构建完成"

# 清理构建文件
clean:
	@echo "🧹 清理构建文件..."
	rm -f digwis-panel
	rm -f assets/css/output.css
	rm -rf tmp/
	@echo "✅ 清理完成"

# 清理所有文件（包括 releases 和 tools）
clean-all:
	@echo "🧹 清理所有构建文件..."
	rm -f digwis-panel
	rm -f assets/css/output.css
	rm -rf tmp/
	rm -rf releases/
	rm -rf tools/
	@echo "✅ 全部清理完成"

# 显示文件大小
size:
	@echo "📊 静态文件大小:"
	@ls -lh assets/css/output.css assets/js/*.js 2>/dev/null || echo "请先构建 CSS"
	@echo ""
	@echo "📊 总计大小:"
	@du -ch assets/css/output.css assets/js/*.js 2>/dev/null | tail -1 || echo "请先构建 CSS"

# 运行测试
test:
	@echo "🧪 运行测试..."
	go test ./...

# 快速启动 (无热重载)
run-local:
	@echo "🚀 快速启动 DigWis Panel"
	@echo "========================"
	@echo "🔨 生成模板文件..."
	@if [ -f "$$HOME/local/go/bin/go" ]; then \
		PATH=$$HOME/local/go/bin:$$PATH GOPROXY=https://goproxy.cn,direct $$HOME/local/go/bin/go run github.com/a-h/templ/cmd/templ@latest generate; \
	else \
		PATH=$$HOME/local/go/bin:$$PATH GOPROXY=https://goproxy.cn,direct go run github.com/a-h/templ/cmd/templ@latest generate; \
	fi
	@echo "🎨 构建 CSS 文件..."
	@npm run build-css-prod
	@echo "🚀 启动服务器 (端口 9090)..."
	@if [ -f "$$HOME/local/go/bin/go" ]; then \
		$$HOME/local/go/bin/go run main.go -port 9090 -host 0.0.0.0 -debug; \
	else \
		go run main.go -port 9090 -host 0.0.0.0 -debug; \
	fi

# 智能安装 Air 到项目本地
install-air:
	@if [ ! -f "$(AIR_PATH)" ]; then \
		echo "📦 安装 Air 到项目本地..."; \
		mkdir -p tools; \
		if [ -f "$$HOME/local/go/bin/go" ]; then \
			export GOBIN=$$(pwd)/tools && export GOPROXY=https://goproxy.cn,direct && $$HOME/local/go/bin/go install github.com/air-verse/air@latest; \
		elif command -v go >/dev/null 2>&1; then \
			export GOBIN=$$(pwd)/tools && export GOPROXY=https://goproxy.cn,direct && go install github.com/air-verse/air@latest; \
		else \
			echo "❌ 未找到 Go 环境，请先安装 Go"; \
			exit 1; \
		fi; \
		echo "✅ Air 安装完成: $(AIR_PATH)"; \
	else \
		echo "✅ Air 已存在: $(AIR_PATH)"; \
	fi



# 快速部署到本地生产环境
deploy-local:
	@echo "🔨 编译嵌入式版本..."
	CGO_ENABLED=1 go build -o digwis-panel .
	@echo "🛑 停止服务..."
	sudo systemctl stop digwis-panel
	@echo "📦 备份当前版本..."
	sudo cp /opt/digwis-panel/digwis-panel /opt/digwis-panel/digwis-panel.backup.$$(date +%s) 2>/dev/null || true
	@echo "🔄 替换程序文件..."
	sudo cp ./digwis-panel /opt/digwis-panel/digwis-panel
	@echo "🚀 启动服务..."
	sudo systemctl start digwis-panel
	@echo "✅ 部署完成！访问: http://localhost:8080"
	@echo ""
	@echo "📊 服务状态："
	@systemctl status digwis-panel --no-pager -l

# 回滚到上一个版本
rollback:
	@echo "🔍 查找备份文件..."
	@BACKUP_FILE=$$(ls -t /opt/digwis-panel/digwis-panel.backup.* 2>/dev/null | head -1); \
	if [ -z "$$BACKUP_FILE" ]; then \
		echo "❌ 没有找到备份文件"; \
		exit 1; \
	fi; \
	echo "🛑 停止服务..."; \
	sudo systemctl stop digwis-panel; \
	echo "🔄 恢复备份版本: $$BACKUP_FILE"; \
	sudo cp "$$BACKUP_FILE" /opt/digwis-panel/digwis-panel; \
	echo "🚀 启动服务..."; \
	sudo systemctl start digwis-panel; \
	echo "✅ 回滚完成！"; \
	systemctl status digwis-panel --no-pager -l

# Screen 开发环境管理
dev-screen:
	@echo "🚀 启动 Screen 开发环境"
	@echo "=================================="
	@if screen -list | grep -q "digwis-dev"; then \
		echo "⚠️  Screen 会话 'digwis-dev' 已存在"; \
		echo "🔗 连接到现有会话: screen -r digwis-dev"; \
		screen -r digwis-dev; \
	else \
		echo "📱 创建新的 Screen 会话..."; \
		screen -S digwis-dev -c /dev/null bash -c 'cd /media/psf/Linux-86/digwis-panel && make dev; exec bash'; \
	fi

dev-screen-detach:
	@echo "🔌 分离 Screen 会话 (服务继续运行)"
	@screen -S digwis-dev -X detach 2>/dev/null || echo "❌ 没有找到活跃的 digwis-dev 会话"

dev-screen-attach:
	@echo "🔗 连接到 Screen 开发会话"
	@screen -r digwis-dev || echo "❌ 没有找到 digwis-dev 会话，请先运行 'make dev-screen'"

dev-screen-status:
	@echo "📊 Screen 会话状态:"
	@screen -list | grep digwis || echo "❌ 没有找到 digwis 相关会话"
	@echo ""
	@echo "🔍 检查服务状态:"
	@if pgrep -f "digwis-panel.*9090" > /dev/null; then \
		echo "✅ DigWis Panel 服务正在运行 (端口 9090)"; \
		echo "🌐 访问地址: http://$(hostname -I | awk '{print $$1}'):9090"; \
	else \
		echo "❌ DigWis Panel 服务未运行"; \
	fi

dev-screen-stop:
	@echo "🛑 停止 Screen 开发环境"
	@screen -S digwis-dev -X quit 2>/dev/null && echo "✅ Screen 会话已终止" || echo "❌ 没有找到活跃的会话"

dev-screen-restart:
	@echo "🔄 重启 Screen 开发环境"
	@make dev-screen-stop
	@sleep 2
	@make dev-screen

# 开发提示
dev-help:
	@echo "💡 开发环境使用方法:"
	@echo ""
	@echo "🖥️  本地开发:"
	@echo "   make dev                    # 启动 Air 热重载 (推荐)"
	@echo "   air                         # 直接使用 Air (如果已安装)"
	@echo "   go run main.go              # 直接运行 (无热重载)"
	@echo ""
	@echo "📱 Screen 开发 (VPS推荐):"
	@echo "   make dev-screen             # 启动/连接 Screen 开发环境"
	@echo "   make dev-screen-attach      # 连接到现有 Screen 会话"
	@echo "   make dev-screen-detach      # 分离 Screen 会话 (保持运行)"
	@echo "   make dev-screen-status      # 查看 Screen 和服务状态"
	@echo "   make dev-screen-stop        # 停止 Screen 开发环境"
	@echo "   make dev-screen-restart     # 重启 Screen 开发环境"
	@echo ""
	@echo "⌨️  Screen 快捷键:"
	@echo "   Ctrl+A, D                   # 分离会话 (服务继续运行)"
	@echo "   Ctrl+A, K                   # 终止会话"
	@echo "   Ctrl+A, ?                   # 显示帮助"

# 部署相关命令 (使用 releases 版本)
deploy: release-build
	@echo "🚀 开始部署..."
	@chmod +x scripts/deploy.sh
	@./scripts/deploy.sh

# 生成安装脚本
install-script:
	@echo "📦 生成安装脚本..."
	@chmod +x scripts/deploy.sh
	@./scripts/deploy.sh --install-script-only

# 推送代码到远程仓库 (使用 releases 版本)
push: release-build
	@echo "📤 推送代码到远程仓库..."
	@chmod +x scripts/simple-push.sh
	@./scripts/simple-push.sh

# 快速推送（带自定义提交信息）
push-msg: release-build
	@echo "📤 推送代码到远程仓库..."
	@chmod +x scripts/simple-push.sh
	@read -p "请输入提交信息: " msg; \
	./scripts/simple-push.sh "$$msg"

# 配置 Git 凭据
git-config:
	@echo "🔐 配置 Git 凭据..."
	@chmod +x scripts/git-push.sh
	@./scripts/git-push.sh --config-only

# 初始化仓库
git-init:
	@echo "🔧 初始化 Git 仓库..."
	@git init
	@chmod +x scripts/git-push.sh
	@./scripts/git-push.sh --config-only
	@echo "✅ Git 仓库初始化完成"

# 版本管理
version:
	@echo "🏷️  版本管理..."
	@chmod +x scripts/version.sh
	@./scripts/version.sh

# 创建版本标签
tag:
	@echo "🏷️  创建版本标签..."
	@chmod +x scripts/version.sh
	@read -p "请输入版本号 (如 v1.0.1): " version; \
	./scripts/version.sh tag "$$version"

# 发布版本 (标签 + 构建 + 推送)
release-version: release-build
	@echo "🚀 发布新版本..."
	@chmod +x scripts/version.sh
	@read -p "请输入版本号 (如 v1.0.1): " version; \
	./scripts/version.sh tag "$$version" && \
	./scripts/version.sh push && \
	make release && \
	echo "✅ 版本 $$version 发布完成"

# 检查 Git 状态
git-status:
	@echo "📋 Git 状态:"
	@git status --short

# 显示详细帮助信息
help-all:
	@echo "🛠️  DigWis Panel 完整构建工具"
	@echo "============================="
	@echo ""
	@echo "📦 发布命令:"
	@echo "   make release-build  - 构建发布包到 releases 目录"
	@echo ""
	@echo "🚀 部署命令:"
	@echo "   make deploy         - 完整部署 (构建 + 推送代码 + 生成安装脚本)"
	@echo "   make push           - 推送代码到远程仓库"
	@echo "   make push-msg       - 推送代码到远程仓库 (自定义提交信息)"
	@echo "   make install-script - 仅生成安装脚本"
	@echo ""
	@echo "🔧 Git 命令:"
	@echo "   make git-config     - 配置 Git 凭据"
	@echo "   make git-init       - 初始化 Git 仓库"
	@echo ""
	@echo "🏷️  版本管理:"
	@echo "   make version        - 版本管理工具"
	@echo "   make tag            - 创建版本标签"
	@echo "   make release-version - 发布新版本 (标签 + 构建 + 推送)"
	@echo ""
	@echo "🧹 清理命令:"
	@echo "   make clean          - 清理构建文件 (保留 releases/)"
	@echo "   make clean-all      - 清理所有文件 (包括 releases/)"
	@echo ""
	@echo "💡 开发帮助:"
	@echo "   make dev-help       - 显示开发环境使用方法"

# 🔍 项目检测 - 验证是否在正确的项目目录
check-project:
	@if [ ! -f "go.mod" ]; then \
		echo "❌ 错误: 当前目录不是 Go 项目根目录"; \
		echo "📁 当前路径: $(PROJECT_ROOT)"; \
		echo "💡 请在包含 go.mod 的项目根目录中运行 make 命令"; \
		exit 1; \
	fi
	@if [ ! -f "main.go" ]; then \
		echo "❌ 警告: 未找到 main.go 文件"; \
	fi
	@echo "✅ 项目检测通过: $(PROJECT_NAME)"

# ⚡ 便捷别名 - 让启动更简单
start: check-project dev
serve: check-project dev
watch: check-project dev
hot: check-project dev

# 🔧 工具别名
t: test
c: clean
b: build
i: install

# 📝 显示当前配置 - 智能路径识别
status:
	@echo "📊 $(PROJECT_NAME) 项目状态"
	@echo "========================"
	@echo "📁 项目名称: $(PROJECT_NAME)"
	@echo "📂 项目路径: $(PROJECT_ROOT)"
	@echo "🔧 Air 路径: $(PROJECT_ROOT)/$(AIR_PATH)"
	@echo "📦 Air 状态: $(shell if [ -f '$(AIR_PATH)' ]; then echo '✅ 已安装'; else echo '❌ 未安装 (运行 make install-air 安装)'; fi)"
	@echo "🐹 Go 版本: $(shell if [ -f '$$HOME/local/go/bin/go' ]; then $$HOME/local/go/bin/go version 2>/dev/null | cut -d' ' -f3 || echo '❌ 未找到'; else go version 2>/dev/null | cut -d' ' -f3 || echo '❌ 未找到'; fi)"
	@echo "📦 Node 版本: $(shell node --version 2>/dev/null || echo '❌ 未找到')"
	@echo "📦 npm 版本: $(shell npm --version 2>/dev/null || echo '❌ 未找到')"
	@echo "========================"
	@echo "🌐 开发服务器: http://localhost:9090"
	@echo "📝 配置文件: $(PROJECT_ROOT)/.air.toml"

# 🔍 环境检测 - 检查开发环境是否完整
check-env:
	@echo "🔍 检查开发环境..."
	@echo "===================="
	@if [ -f "$$HOME/local/go/bin/go" ]; then \
		echo "✅ Go: $$($$HOME/local/go/bin/go version | cut -d' ' -f3)"; \
	elif command -v go >/dev/null 2>&1; then \
		echo "✅ Go: $$(go version | cut -d' ' -f3)"; \
	else \
		echo "❌ Go: 未安装"; \
		echo "💡 请安装 Go 1.21+ 版本"; \
	fi
	@if command -v node >/dev/null 2>&1; then \
		echo "✅ Node.js: $$(node --version)"; \
	else \
		echo "❌ Node.js: 未安装"; \
		echo "💡 请安装 Node.js 18+ 版本"; \
	fi
	@if command -v npm >/dev/null 2>&1; then \
		echo "✅ npm: $$(npm --version)"; \
	else \
		echo "❌ npm: 未安装"; \
	fi
	@if [ -f "$(AIR_PATH)" ]; then \
		echo "✅ Air: 已安装在 $(AIR_PATH)"; \
	else \
		echo "❌ Air: 未安装"; \
		echo "💡 运行 'make install-air' 安装"; \
	fi
	@if [ -f "package.json" ]; then \
		echo "✅ package.json: 存在"; \
	else \
		echo "❌ package.json: 不存在"; \
	fi
	@if [ -f ".air.toml" ]; then \
		echo "✅ .air.toml: 存在"; \
	else \
		echo "❌ .air.toml: 不存在"; \
	fi
	@echo "===================="

# 🚀 完整的环境检查和启动
dev-check: check-project check-env dev
