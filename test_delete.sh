#!/bin/bash

# 测试项目删除功能的脚本

BASE_URL="http://localhost:9090"
PROJECT_ID="test-project-$(date +%s)"

echo "🧪 测试项目删除功能"
echo "项目ID: $PROJECT_ID"

# 1. 先登录获取session
echo "1. 登录..."
LOGIN_RESPONSE=$(curl -s -c cookies.txt -X POST "$BASE_URL/api/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin"}')

echo "登录响应: $LOGIN_RESPONSE"

# 2. 创建测试项目目录
echo "2. 创建测试项目目录..."
sudo mkdir -p "/var/www/$PROJECT_ID"
echo "<h1>Test Project</h1>" | sudo tee "/var/www/$PROJECT_ID/index.html" > /dev/null

# 3. 测试删除项目
echo "3. 测试删除项目..."
DELETE_RESPONSE=$(curl -s -b cookies.txt -X DELETE "$BASE_URL/api/projects/$PROJECT_ID/delete" \
  -H "Content-Type: application/json")

echo "删除响应: $DELETE_RESPONSE"

# 4. 检查项目目录是否被删除
echo "4. 检查项目目录是否被删除..."
if [ -d "/var/www/$PROJECT_ID" ]; then
    echo "❌ 项目目录仍然存在"
    ls -la "/var/www/$PROJECT_ID"
else
    echo "✅ 项目目录已被删除"
fi

# 清理
rm -f cookies.txt

echo "🏁 测试完成"
