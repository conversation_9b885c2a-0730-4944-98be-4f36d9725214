package handlers

import (
	"encoding/json"
	"fmt"
	"net"
	"net/http"
	"os/user"
	"strings"
	"time"

	"server-panel/internal/auth"
	"server-panel/internal/config"
	"server-panel/internal/i18n"
	"server-panel/internal/session"
	"server-panel/internal/system"
	"server-panel/internal/environment"
	"server-panel/internal/projects"
	"server-panel/internal/templates/pages"
	"server-panel/internal/templates/components"
)

// Handlers 处理器
type Handlers struct {
	systemMonitor  *system.Monitor
	envManager     *environment.Manager
	projectManager *projects.Manager
	sessionStore   *session.Store
	authManager    *auth.Manager
}

// NewHandlers 创建处理器
func NewHandlers(systemMonitor *system.Monitor, envManager *environment.Manager, projectManager *projects.Manager) *Handlers {
	// 初始化会话存储
	store := session.NewStore()

	// 初始化认证管理器
	authConfig := config.AuthConfig{
		SessionTimeout:   time.Hour * 24,        // 24小时会话超时
		MaxLoginAttempts: 5,                     // 最大登录尝试次数
		LockoutDuration:  time.Minute * 15,      // 15分钟锁定时间
		SecretKey:        "digwis-panel-secret", // 会话密钥
	}
	authManager := auth.NewManager(authConfig)

	return &Handlers{
		systemMonitor:  systemMonitor,
		envManager:     envManager,
		projectManager: projectManager,
		sessionStore:   store,
		authManager:    authManager,
	}
}

// GetSessionStore 获取会话存储
func (h *Handlers) GetSessionStore() *session.Store {
	return h.sessionStore
}



// writeJSON 写入JSON响应
func writeJSON(w http.ResponseWriter, status int, data interface{}) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(status)
	json.NewEncoder(w).Encode(data)
}

// getCurrentUser 获取当前用户
func getCurrentUser() string {
	if user, err := user.Current(); err == nil {
		return user.Username
	}
	return "unknown"
}



// LoginPage 登录页面
func (h *Handlers) LoginPage(w http.ResponseWriter, r *http.Request) {
	if r.Method == "POST" {
		h.handleLogin(w, r)
		return
	}

	// 检查是否有错误消息
	errorMsg := r.URL.Query().Get("error")

	// 使用templ模板渲染登录页面
	component := pages.Login("DigWis Panel - 登录", errorMsg)
	component.Render(r.Context(), w)
}



// handleLogin 处理登录
func (h *Handlers) handleLogin(w http.ResponseWriter, r *http.Request) {
	username := r.FormValue("username")
	password := r.FormValue("password")

	// 获取客户端IP地址
	clientIP := r.RemoteAddr
	if forwarded := r.Header.Get("X-Forwarded-For"); forwarded != "" {
		clientIP = forwarded
	}

	// 使用系统用户认证
	authSession, err := h.authManager.Authenticate(username, password, clientIP)
	if err != nil {
		// 认证失败
		if r.Header.Get("Content-Type") == "application/json" ||
		   r.Header.Get("HX-Request") == "true" {
			writeJSON(w, http.StatusUnauthorized, map[string]interface{}{
				"success": false,
				"error":   err.Error(),
			})
		} else {
			// 重定向到登录页面并显示错误
			http.Redirect(w, r, "/login?error="+err.Error(), http.StatusFound)
		}
		return
	}

	// 认证成功，创建会话
	sess, err := h.sessionStore.Get(r)
	if err != nil {
		writeJSON(w, http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   "会话创建失败",
		})
		return
	}

	sess.Set("username", authSession.Username)
	sess.Set("authenticated", true)
	sess.Set("auth_session_id", authSession.ID)
	sess.Set("login_time", authSession.LoginTime)

	if err := sess.SaveWithStore(w, h.sessionStore); err != nil {
		writeJSON(w, http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   "会话保存失败",
		})
		return
	}

	// 检查请求类型
	if r.Header.Get("Content-Type") == "application/json" ||
	   r.Header.Get("HX-Request") == "true" {
		writeJSON(w, http.StatusOK, map[string]interface{}{
			"success": true,
			"message": "登录成功",
			"redirect": "/dashboard",
		})
	} else {
		http.Redirect(w, r, "/dashboard", http.StatusFound)
	}
}

// APILogin API 登录
func (h *Handlers) APILogin(w http.ResponseWriter, r *http.Request) {
	var username, password string

	// 检查Content-Type来决定如何解析数据
	contentType := r.Header.Get("Content-Type")

	if strings.Contains(contentType, "application/json") {
		// JSON格式
		var credentials struct {
			Username string `json:"username"`
			Password string `json:"password"`
		}
		if err := json.NewDecoder(r.Body).Decode(&credentials); err != nil {
			writeJSON(w, http.StatusBadRequest, map[string]interface{}{
				"success": false,
				"error":   "无效的JSON数据",
			})
			return
		}
		username = credentials.Username
		password = credentials.Password
	} else {
		// Form数据格式
		if err := r.ParseForm(); err != nil {
			writeJSON(w, http.StatusBadRequest, map[string]interface{}{
				"success": false,
				"error":   "无效的表单数据",
			})
			return
		}
		username = r.FormValue("username")
		password = r.FormValue("password")
	}

	// 获取客户端IP地址
	clientIP := r.RemoteAddr
	if forwarded := r.Header.Get("X-Forwarded-For"); forwarded != "" {
		clientIP = forwarded
	}

	// 使用系统用户认证
	authSession, err := h.authManager.Authenticate(username, password, clientIP)
	if err != nil {
		writeJSON(w, http.StatusUnauthorized, map[string]interface{}{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// 认证成功，创建会话
	sess, err := h.sessionStore.Get(r)
	if err != nil {
		writeJSON(w, http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   "会话创建失败",
		})
		return
	}

	sess.Set("username", authSession.Username)
	sess.Set("authenticated", true)
	sess.Set("auth_session_id", authSession.ID)
	sess.Set("login_time", authSession.LoginTime)

	if err := sess.SaveWithStore(w, h.sessionStore); err != nil {
		writeJSON(w, http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   "会话保存失败",
		})
		return
	}

	// 检查是否是HTMX请求
	if r.Header.Get("HX-Request") == "true" {
		// 返回重定向指令给HTMX
		w.Header().Set("HX-Redirect", "/dashboard")
		writeJSON(w, http.StatusOK, map[string]interface{}{
			"success": true,
			"message": "登录成功",
			"redirect": "/dashboard",
		})
	} else {
		writeJSON(w, http.StatusOK, map[string]interface{}{
			"success": true,
			"message": "登录成功",
			"user": map[string]interface{}{
				"username": authSession.Username,
				"login_time": authSession.LoginTime,
			},
		})
	}
}

// Dashboard 仪表板
func (h *Handlers) Dashboard(w http.ResponseWriter, r *http.Request) {
	// 从会话中获取用户名
	sess, err := h.sessionStore.Get(r)
	if err != nil {
		http.Redirect(w, r, "/login", http.StatusFound)
		return
	}

	username := sess.Get("username")
	if username == nil {
		http.Redirect(w, r, "/login", http.StatusFound)
		return
	}

	// 获取当前语言
	currentLang := i18n.GetLanguageFromRequest(r)

	// 使用templ模板渲染仪表板
	usernameStr := fmt.Sprintf("%v", username)
	title := fmt.Sprintf("DigWis Panel - %s", i18n.T(currentLang, "dashboard.title"))
	component := pages.Dashboard(title, usernameStr, currentLang)
	component.Render(r.Context(), w)
}

// SystemPage 系统页面
func (h *Handlers) SystemPage(w http.ResponseWriter, r *http.Request) {
	// 从会话中获取用户名
	sess, err := h.sessionStore.Get(r)
	if err != nil {
		http.Redirect(w, r, "/login", http.StatusFound)
		return
	}

	username := sess.Get("username")
	if username == nil {
		http.Redirect(w, r, "/login", http.StatusFound)
		return
	}

	// 获取当前语言
	currentLang := i18n.GetLanguageFromRequest(r)

	// 使用Dashboard模板作为系统页面（可以后续创建专门的系统页面模板）
	usernameStr := fmt.Sprintf("%v", username)
	title := fmt.Sprintf("DigWis Panel - %s", i18n.T(currentLang, "nav.system"))
	component := pages.Dashboard(title, usernameStr, currentLang)
	component.Render(r.Context(), w)
}

// SetLanguage 设置语言处理器
func (h *Handlers) SetLanguage(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	lang := r.FormValue("lang")
	if lang == "" {
		lang = r.URL.Query().Get("lang")
	}

	// 设置语言 Cookie
	i18n.SetLanguageCookie(w, lang)

	// 返回成功响应
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	w.Write([]byte(`{"success": true}`))
}

// ProjectsPage 项目页面
func (h *Handlers) ProjectsPage(w http.ResponseWriter, r *http.Request) {
	// 从会话中获取用户名
	sess, err := h.sessionStore.Get(r)
	if err != nil {
		http.Redirect(w, r, "/login", http.StatusFound)
		return
	}

	username := sess.Get("username")
	if username == nil {
		http.Redirect(w, r, "/login", http.StatusFound)
		return
	}

	// 获取项目概览
	overview, err := h.projectManager.GetOverview()
	if err != nil {
		// 如果获取失败，创建一个空的概览
		overview = &projects.ProjectOverview{
			Projects:       []projects.Project{},
			TotalProjects:  0,
			ActiveProjects: 0,
			TotalSize:      0,
			FirstTimeSetup: true,
		}
	}

	// 获取当前语言
	currentLang := i18n.GetLanguageFromRequest(r)

	// 使用templ模板渲染项目页面
	usernameStr := fmt.Sprintf("%v", username)
	title := fmt.Sprintf("DigWis Panel - %s", i18n.T(currentLang, "projects.title"))
	component := pages.Projects(title, usernameStr, currentLang, overview)
	component.Render(r.Context(), w)
}

// EnvironmentPage 环境页面
func (h *Handlers) EnvironmentPage(w http.ResponseWriter, r *http.Request) {
	// 从会话中获取用户名
	sess, err := h.sessionStore.Get(r)
	if err != nil {
		http.Redirect(w, r, "/login", http.StatusFound)
		return
	}

	username := sess.Get("username")
	if username == nil {
		http.Redirect(w, r, "/login", http.StatusFound)
		return
	}

	// 获取环境概览
	overview, err := h.envManager.GetOverview()
	if err != nil {
		// 如果获取失败，创建一个空的概览
		overview = &environment.EnvironmentOverview{}
	}

	// 获取当前语言
	currentLang := i18n.GetLanguageFromRequest(r)

	// 使用templ模板渲染环境页面
	usernameStr := fmt.Sprintf("%v", username)
	title := fmt.Sprintf("DigWis Panel - %s", i18n.T(currentLang, "environment.title"))
	component := pages.Environment(title, usernameStr, currentLang, overview)
	component.Render(r.Context(), w)
}

// SystemStats 系统统计
func (h *Handlers) SystemStats(w http.ResponseWriter, r *http.Request) {
	stats, err := h.systemMonitor.GetSystemStats()
	if err != nil {
		writeJSON(w, http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	writeJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"data":    stats,
	})
}

// StatsDetails 统计详情
func (h *Handlers) StatsDetails(w http.ResponseWriter, r *http.Request) {
	// 从 URL 参数中获取类型
	statsType := r.URL.Query().Get("type")
	if statsType == "" {
		writeJSON(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "缺少统计类型参数",
		})
		return
	}

	// 根据类型返回详细信息
	details := map[string]interface{}{
		"type": statsType,
		"data": "详细统计数据",
	}

	writeJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"data":    details,
	})
}

// CPUChart CPU 图表数据
func (h *Handlers) CPUChart(w http.ResponseWriter, r *http.Request) {
	stats, err := h.systemMonitor.GetSystemStats()
	if err != nil {
		writeJSON(w, http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	writeJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"data":    stats,
	})
}

// SystemOverview 系统概览
func (h *Handlers) SystemOverview(w http.ResponseWriter, r *http.Request) {
	stats, err := h.systemMonitor.GetSystemStats()
	if err != nil {
		writeJSON(w, http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	writeJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"data":    stats,
	})
}

// SystemDetails 系统详情
func (h *Handlers) SystemDetails(w http.ResponseWriter, r *http.Request) {
	details := h.systemMonitor.GetSystemDetails()
	writeJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"data":    details,
	})
}

// ProcessList 进程列表
func (h *Handlers) ProcessList(w http.ResponseWriter, r *http.Request) {
	processes, err := h.systemMonitor.GetProcessList()
	if err != nil {
		writeJSON(w, http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	writeJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"data":    processes,
	})
}

// ProjectsScan 项目扫描
func (h *Handlers) ProjectsScan(w http.ResponseWriter, r *http.Request) {
	projects, err := h.projectManager.ScanProjects()
	if err != nil {
		writeJSON(w, http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	writeJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"data":    projects,
	})
}

// ProjectCreateForm 项目创建表单
func (h *Handlers) ProjectCreateForm(w http.ResponseWriter, r *http.Request) {
	// 获取当前语言
	currentLang := i18n.GetLanguageFromRequest(r)

	// 如果是 HTMX 请求，返回表单 HTML
	if r.Header.Get("HX-Request") == "true" {
		// 使用 templ 组件渲染表单
		component := components.ProjectCreateForm(currentLang)
		component.Render(r.Context(), w)
		return
	}

	// 否则返回 JSON（向后兼容）
	form := map[string]interface{}{
		"name":        "",
		"description": "",
		"type":        "web",
	}

	writeJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"data":    form,
	})
}

// ProjectCreate 创建项目
func (h *Handlers) ProjectCreate(w http.ResponseWriter, r *http.Request) {
	var req projects.CreateProjectRequest

	// 检查是否是 HTMX 表单请求
	if r.Header.Get("HX-Request") == "true" && r.Method == "POST" {
		// 解析表单数据
		if err := r.ParseForm(); err != nil {
			http.Error(w, "解析表单失败", http.StatusBadRequest)
			return
		}

		req.Name = r.FormValue("name")
		req.Domain = r.FormValue("domain")
		req.CreateDB = r.FormValue("createDB") == "true"
		req.EnableSSL = r.FormValue("enableSSL") == "true"
		req.EnableBackup = r.FormValue("enableBackup") == "true"
	} else {
		// 解析JSON请求（向后兼容）
		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			writeJSON(w, http.StatusBadRequest, map[string]interface{}{
				"success": false,
				"error":   "无效的请求格式: " + err.Error(),
			})
			return
		}
	}

	// 创建项目
	project, err := h.projectManager.CreateProject(req)
	if err != nil {
		if r.Header.Get("HX-Request") == "true" {
			// HTMX 请求返回错误页面或重新加载页面
			http.Redirect(w, r, "/projects?error="+err.Error(), http.StatusSeeOther)
			return
		}
		writeJSON(w, http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// 成功处理
	if r.Header.Get("HX-Request") == "true" {
		// HTMX 请求重定向到项目页面
		w.Header().Set("HX-Redirect", "/projects")
		w.WriteHeader(http.StatusOK)
		return
	}

	// JSON 响应（向后兼容）
	writeJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"message": "项目创建成功",
		"data":    project,
	})
}

// ProjectGet 获取单个项目
func (h *Handlers) ProjectGet(w http.ResponseWriter, r *http.Request) {
	// 从URL路径中获取项目ID
	projectID := r.URL.Query().Get("id")
	if projectID == "" {
		// 如果查询参数中没有，尝试从路径中获取
		path := strings.TrimPrefix(r.URL.Path, "/api/projects/")
		if path != "" && !strings.Contains(path, "/") {
			projectID = path
		}
	}
	if projectID == "" {
		writeJSON(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "项目ID不能为空",
		})
		return
	}

	// 首先尝试从数据库获取项目信息
	project, err := h.projectManager.GetProjectFromDB(projectID)
	if err != nil {
		// 如果数据库中没有，尝试从文件系统获取（向后兼容）
		legacyProject, legacyErr := h.projectManager.GetProject(projectID)
		if legacyErr != nil {
			writeJSON(w, http.StatusNotFound, map[string]interface{}{
				"success": false,
				"error":   "项目不存在",
			})
			return
		}
		
		// 将旧项目转换为新格式并存储到数据库
		newProject := &projects.Project{
			ID:           legacyProject.ID,
			Name:         legacyProject.Name,
			Path:         legacyProject.Path,
			Type:         legacyProject.Type,
			Status:       legacyProject.Status,
			Domain:       legacyProject.Domain,
			Aliases:      []string{},
			DocumentRoot: legacyProject.Path,
			SSLEnabled:   false,
			SSLMethod:    "letsencrypt",
			PHPEnabled:   legacyProject.PHPVersion != "",
			PHPVersion:   legacyProject.PHPVersion,
			IndexFiles:   "index.html index.php",
			CustomNginx:  "",
			Description:  legacyProject.Description,
			Size:         legacyProject.Size,
			Database:     legacyProject.Database,
			CreatedAt:    legacyProject.CreatedAt,
			UpdatedAt:    legacyProject.UpdatedAt,
		}
		
		// 尝试保存到数据库（忽略错误）
		h.projectManager.CreateProjectInDB(newProject)
		project = newProject
	}

	writeJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"data":    project,
	})
}

// ProjectSaveConfig 保存项目配置
func (h *Handlers) ProjectSaveConfig(w http.ResponseWriter, r *http.Request) {
	// 从URL查询参数中获取项目ID（路由器会将路径参数转换为查询参数）
	projectID := r.URL.Query().Get("id")
	if projectID == "" {
		writeJSON(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "项目 ID 不能为空",
		})
		return
	}

	// 解析请求体
	var config struct {
		Domain       string   `json:"domain"`
		Aliases      []string `json:"aliases"`
		DocumentRoot string   `json:"documentRoot"`
		SSLEnabled   bool     `json:"sslEnabled"`
		SSLMethod    string   `json:"sslMethod"`
		PHPEnabled   bool     `json:"phpEnabled"`
		PHPVersion   string   `json:"phpVersion"`
		IndexFiles   string   `json:"indexFiles"`
		CustomNginx  string   `json:"customNginx"`
	}

	if err := json.NewDecoder(r.Body).Decode(&config); err != nil {
		writeJSON(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "无效的请求数据",
		})
		return
	}

	// 获取现有项目
	project, err := h.projectManager.GetProjectFromDB(projectID)
	if err != nil {
		// 如果数据库中没有，尝试从文件系统获取
		legacyProject, legacyErr := h.projectManager.GetProject(projectID)
		if legacyErr != nil {
			writeJSON(w, http.StatusNotFound, map[string]interface{}{
				"success": false,
				"error":   "项目不存在",
			})
			return
		}
		
		// 创建新项目记录
		project = &projects.Project{
			ID:          legacyProject.ID,
			Name:        legacyProject.Name,
			Path:        legacyProject.Path,
			Type:        legacyProject.Type,
			Status:      legacyProject.Status,
			Size:        legacyProject.Size,
			Database:    legacyProject.Database,
			Description: legacyProject.Description,
			CreatedAt:   legacyProject.CreatedAt,
			UpdatedAt:   legacyProject.UpdatedAt,
		}
	}

	// 更新配置
	project.Domain = config.Domain
	project.Aliases = config.Aliases
	project.DocumentRoot = config.DocumentRoot
	project.SSLEnabled = config.SSLEnabled
	project.SSLMethod = config.SSLMethod
	project.PHPEnabled = config.PHPEnabled
	project.PHPVersion = config.PHPVersion
	project.IndexFiles = config.IndexFiles
	project.CustomNginx = config.CustomNginx

	// 保存到数据库
	if err := h.projectManager.UpdateProjectInDB(project); err != nil {
		// 如果更新失败，尝试创建
		if createErr := h.projectManager.CreateProjectInDB(project); createErr != nil {
			writeJSON(w, http.StatusInternalServerError, map[string]interface{}{
				"success": false,
				"error":   "保存配置失败",
			})
			return
		}
	}

	writeJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"message": "配置保存成功",
		"data":    project,
	})
}

// ProjectDelete 删除项目
func (h *Handlers) ProjectDelete(w http.ResponseWriter, r *http.Request) {
	// 从URL查询参数中获取项目ID（路由器会将路径参数转换为查询参数）
	projectID := r.URL.Query().Get("id")
	if projectID == "" {
		writeJSON(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "项目ID不能为空",
		})
		return
	}

	// 先从数据库删除项目记录
	if err := h.projectManager.DeleteProjectFromDB(projectID); err != nil {
		// 如果数据库删除失败，记录错误但继续删除文件
		fmt.Printf("Warning: Failed to delete project from database: %v\n", err)
	}

	// 删除项目文件和配置
	err := h.projectManager.DeleteProject(projectID)
	if err != nil {
		writeJSON(w, http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	writeJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"message": "项目删除成功",
	})
}

// ProjectsConfig 获取项目配置信息
func (h *Handlers) ProjectsConfig(w http.ResponseWriter, r *http.Request) {
	// 获取项目目录路径
	projectsDir := h.projectManager.GetProjectsDir()

	// 获取服务器信息
	serverInfo := h.getServerInfo(r)

	writeJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"data": map[string]interface{}{
			"projects_dir": projectsDir,
			"server_info":  serverInfo,
		},
	})
}

// getServerInfo 获取服务器信息
func (h *Handlers) getServerInfo(r *http.Request) map[string]interface{} {
	// 获取服务器IP和端口
	host := r.Host
	if host == "" {
		host = "localhost:9090" // 默认值
	}

	// 尝试获取真实的服务器IP
	serverIP := h.getServerIP()

	return map[string]interface{}{
		"host":      host,
		"server_ip": serverIP,
		"default_domain_format": serverIP + ":9090", // 默认域名格式
	}
}

// getServerIP 获取服务器IP地址
func (h *Handlers) getServerIP() string {
	// 尝试多种方法获取服务器IP

	// 方法1: 尝试获取外网IP（通过连接外部服务）
	if ip := h.getExternalIP(); ip != "" {
		return ip
	}

	// 方法2: 获取本地网络接口IP
	if ip := h.getLocalIP(); ip != "" {
		return ip
	}

	// 方法3: 默认返回localhost
	return "localhost"
}

// getExternalIP 获取外网IP（通过HTTP请求）
func (h *Handlers) getExternalIP() string {
	// 这里可以调用外部服务获取公网IP，但在开发环境中可能不需要
	// 暂时返回空字符串，让它回退到本地IP
	return ""
}

// getLocalIP 获取本地网络接口IP
func (h *Handlers) getLocalIP() string {
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return ""
	}

	for _, addr := range addrs {
		if ipnet, ok := addr.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipnet.IP.To4() != nil {
				// 优先返回私有网络IP
				ip := ipnet.IP.String()
				if h.isPrivateIP(ip) {
					return ip
				}
			}
		}
	}

	return ""
}

// isPrivateIP 检查是否为私有IP地址
func (h *Handlers) isPrivateIP(ip string) bool {
	privateRanges := []string{
		"10.0.0.0/8",
		"**********/12",
		"***********/16",
	}

	for _, cidr := range privateRanges {
		_, subnet, err := net.ParseCIDR(cidr)
		if err != nil {
			continue
		}
		if subnet.Contains(net.ParseIP(ip)) {
			return true
		}
	}

	return false
}

// EnvironmentStatus 环境状态
func (h *Handlers) EnvironmentStatus(w http.ResponseWriter, r *http.Request) {
	status, err := h.envManager.GetOverview()
	if err != nil {
		writeJSON(w, http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	writeJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"data":    status,
	})
}

// InstallEnvironment 安装环境
func (h *Handlers) InstallEnvironment(w http.ResponseWriter, r *http.Request) {
	var req struct {
		Name    string `json:"name"`
		Version string `json:"version"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		writeJSON(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "无效的请求数据",
		})
		return
	}

	// 创建进度通道
	progressChan := make(chan environment.InstallProgress, 10)
	defer close(progressChan)

	// 启动安装过程
	go func() {
		err := h.envManager.InstallEnvironment(req.Name, req.Version, progressChan)
		if err != nil {
			progressChan <- environment.InstallProgress{
				Environment: req.Name,
				Progress:    0,
				Message:     err.Error(),
				Status:      "error",
			}
		}
	}()

	// 等待安装完成或出错
	var lastProgress environment.InstallProgress
	for progress := range progressChan {
		lastProgress = progress
		if progress.Status == "completed" || progress.Status == "error" {
			break
		}
	}

	if lastProgress.Status == "error" {
		writeJSON(w, http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   lastProgress.Message,
		})
		return
	}

	writeJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"message": fmt.Sprintf("环境 %s 安装成功", req.Name),
	})
}

// UninstallEnvironment 卸载环境
func (h *Handlers) UninstallEnvironment(w http.ResponseWriter, r *http.Request) {
	var req struct {
		Name string `json:"name"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		writeJSON(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "无效的请求数据",
		})
		return
	}

	// 卸载环境
	err := h.envManager.UninstallEnvironment(req.Name)
	if err != nil {
		writeJSON(w, http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	writeJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"message": fmt.Sprintf("环境 %s 卸载成功", req.Name),
	})
}

// UpgradeEnvironment 升级环境
func (h *Handlers) UpgradeEnvironment(w http.ResponseWriter, r *http.Request) {
	var req struct {
		Name    string `json:"name"`
		Version string `json:"version"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		writeJSON(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "无效的请求数据",
		})
		return
	}

	// 创建进度通道
	progressChan := make(chan environment.InstallProgress, 10)
	defer close(progressChan)

	// 启动升级过程
	go func() {
		err := h.envManager.UpgradeEnvironment(req.Name, req.Version, progressChan)
		if err != nil {
			progressChan <- environment.InstallProgress{
				Environment: req.Name,
				Progress:    0,
				Message:     err.Error(),
				Status:      "error",
			}
		}
	}()

	// 等待升级完成或出错
	var lastProgress environment.InstallProgress
	for progress := range progressChan {
		lastProgress = progress
		if progress.Status == "completed" || progress.Status == "error" {
			break
		}
	}

	if lastProgress.Status == "error" {
		writeJSON(w, http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   lastProgress.Message,
		})
		return
	}

	writeJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"message": fmt.Sprintf("环境 %s 升级成功", req.Name),
	})
}

// EnvironmentProgress 获取环境安装进度
func (h *Handlers) EnvironmentProgress(w http.ResponseWriter, r *http.Request) {
	progress := h.envManager.GetProgress()

	if progress == nil {
		writeJSON(w, http.StatusOK, map[string]interface{}{
			"success": true,
			"data":    nil,
		})
		return
	}

	writeJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"data":    progress,
	})
}

// TestSSEHandler 测试 SSE 处理器
func (h *Handlers) TestSSEHandler(w http.ResponseWriter, r *http.Request) {
	// 设置 SSE 头部
	w.Header().Set("Content-Type", "text/event-stream")
	w.Header().Set("Cache-Control", "no-cache")
	w.Header().Set("Connection", "keep-alive")
	w.Header().Set("Access-Control-Allow-Origin", "*")

	// 发送测试数据
	fmt.Fprintf(w, "data: {\"message\": \"SSE 连接测试成功\", \"timestamp\": \"%s\"}\n\n", time.Now().Format(time.RFC3339))

	if flusher, ok := w.(http.Flusher); ok {
		flusher.Flush()
	}
}

// SSEStatsHandler SSE 统计数据处理器 - 临时禁用认证以测试数据流
func (h *Handlers) SSEStatsHandler(w http.ResponseWriter, r *http.Request) {
	// 临时注释认证检查以测试数据流
	/*
	sess, err := h.sessionStore.Get(r)
	if err != nil || sess.Get("authenticated") != true {
		http.Error(w, "未授权访问", http.StatusUnauthorized)
		return
	}
	*/

	// 设置SSE头部
	w.Header().Set("Content-Type", "text/event-stream")
	w.Header().Set("Cache-Control", "no-cache")
	w.Header().Set("Connection", "keep-alive")
	w.Header().Set("X-Accel-Buffering", "no") // 禁用nginx缓冲

	// 设置CORS头部
	origin := r.Header.Get("Origin")
	if origin != "" {
		w.Header().Set("Access-Control-Allow-Origin", origin)
		w.Header().Set("Access-Control-Allow-Credentials", "true")
	}

	// 尝试获取Flusher接口，如果不支持则使用替代方案
	flusher, hasFlusher := w.(http.Flusher)

	// 发送连接建立事件
	fmt.Fprintf(w, "event: connected\ndata: {\"message\": \"SSE连接已建立\", \"hasFlusher\": %t}\n\n", hasFlusher)
	if hasFlusher {
		flusher.Flush()
	}

	// 创建定时器
	ticker := time.NewTicker(3 * time.Second) // 减少到3秒以提高响应性
	defer ticker.Stop()

	// 发送初始数据
	h.sendSystemStatsImproved(w, flusher, hasFlusher)

	// 持续发送数据
	for {
		select {
		case <-ticker.C:
			h.sendSystemStatsImproved(w, flusher, hasFlusher)
		case <-r.Context().Done():
			// 客户端断开连接
			return
		}
	}
}

// sendSystemStatsImproved 发送系统统计数据 - 改进版本
func (h *Handlers) sendSystemStatsImproved(w http.ResponseWriter, flusher http.Flusher, hasFlusher bool) {
	stats, err := h.systemMonitor.GetSystemStats()
	if err != nil {
		fmt.Fprintf(w, "event: error\ndata: {\"error\": \"%s\"}\n\n", err.Error())
		if hasFlusher {
			flusher.Flush()
		}
		return
	}

	// 直接发送stats数据
	data, err := json.Marshal(stats)
	if err != nil {
		fmt.Fprintf(w, "event: error\ndata: {\"error\": \"数据序列化失败\"}\n\n")
		if hasFlusher {
			flusher.Flush()
		}
		return
	}

	// 添加时间戳以便调试
	timestamp := time.Now().Format(time.RFC3339)
	fmt.Fprintf(w, "event: stats\ndata: %s\nid: %s\n\n", data, timestamp)

	if hasFlusher {
		flusher.Flush()
	}
}
