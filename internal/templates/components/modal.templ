package components

templ Modal() {
	<!-- 简化的模态框容器 - 使用纯 CSS 和 JavaScript -->
	<div id="modal" class="fixed inset-0 z-50 overflow-y-auto hidden transition-opacity duration-300">
		<div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
			<!-- 背景遮罩 -->
			<div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onclick="closeModal()"></div>
			<span class="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>
			<!-- 模态框内容 -->
			<div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
				<div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
					<div class="sm:flex sm:items-start">
						<div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
							<div class="flex justify-between items-center mb-4">
								<h3 id="modal-title" class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">
									模态框标题
								</h3>
								<button onclick="closeModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
									<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
									</svg>
								</button>
							</div>
							<div id="modal-content">
								<!-- 动态内容 -->
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- 简化的模态框控制脚本 -->
	<script>
		function openModal(title = '') {
			const modal = document.getElementById('modal');
			const titleEl = document.getElementById('modal-title');
			if (modal) {
				if (titleEl && title) titleEl.textContent = title;
				modal.classList.remove('hidden');
				document.body.style.overflow = 'hidden'; // 防止背景滚动
			}
		}

		function closeModal() {
			const modal = document.getElementById('modal');
			if (modal) {
				modal.classList.add('hidden');
				document.body.style.overflow = ''; // 恢复滚动
			}
		}

		// ESC 键关闭模态框
		document.addEventListener('keydown', function(e) {
			if (e.key === 'Escape') {
				closeModal();
			}
		});
	</script>
}
