package components

import "server-panel/internal/i18n"

templ AddProjectCard(currentLang string) {
	<!-- 新增项目卡片 -->
	<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border-2 border-dashed border-gray-300 dark:border-gray-600 p-6 hover:shadow-md hover:border-blue-400 dark:hover:border-blue-500 transition-all cursor-pointer group"
		 onclick="openNewProjectModal()">
		<div class="flex flex-col items-center justify-center h-full min-h-[200px] text-center">
			<div class="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mb-4 group-hover:bg-blue-200 dark:group-hover:bg-blue-800 transition-colors">
				<svg class="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
				</svg>
			</div>
			<h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">{ i18n.T(currentLang, "projects.add.title") }</h3>
			<p class="text-sm text-gray-500 dark:text-gray-400 max-w-xs">{ i18n.T(currentLang, "projects.add.description") }</p>
		</div>
	</div>

	<script>
		function openNewProjectModal() {
			// 这里可以打开新增项目的模态框
			// 或者跳转到新增项目页面
			console.log('Opening new project modal...');
			// 示例：跳转到新增项目页面
			// window.location.href = '/projects/new';
			
			// 或者触发模态框显示
			// document.getElementById('newProjectModal').style.display = 'block';
		}
	</script>
}
