package components

import (
	"server-panel/internal/projects"
	"fmt"
)

func formatBytes(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

templ ProjectCard(project projects.Project) {
	<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
		<div class="flex items-center justify-between mb-4">
			<div class="flex items-center">
				<div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-3">
					<svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
						<path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
					</svg>
				</div>
				<div>
					<h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">{ project.Name }</h3>
					<p class="text-sm text-gray-500 dark:text-gray-400">{ project.Domain }</p>
				</div>
			</div>
			<div class="flex flex-col items-end">
				if project.Status == "active" {
					<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
						<svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
						</svg>
						Active
					</span>
				} else {
					<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
						Inactive
					</span>
				}
			</div>
		</div>

		<div class="space-y-2 mb-4">
			<div class="flex justify-between text-sm">
				<span class="text-gray-500 dark:text-gray-400">Path:</span>
				<span class="text-gray-900 dark:text-gray-100 font-mono text-xs">{ project.Path }</span>
			</div>
			<div class="flex justify-between text-sm">
				<span class="text-gray-500 dark:text-gray-400">Size:</span>
				<span class="text-gray-900 dark:text-gray-100">{ formatBytes(project.Size) }</span>
			</div>
			<div class="flex justify-between text-sm">
				<span class="text-gray-500 dark:text-gray-400">Created:</span>
				<span class="text-gray-900 dark:text-gray-100">{ project.CreatedAt.Format("2006-01-02") }</span>
			</div>
			if project.Database != "" {
				<div class="flex justify-between text-sm">
					<span class="text-gray-500 dark:text-gray-400">Database:</span>
					<span class="text-gray-900 dark:text-gray-100">{ project.Database }</span>
				</div>
			}
		</div>

		<!-- Action Buttons -->
		<div class="grid grid-cols-2 gap-2">
			<!-- Start/Stop Button -->
			if project.Status == "active" {
				<button 
					data-project={ project.ID }
					data-action="stop"
					class="flex items-center justify-center px-3 py-2 bg-red-600 dark:bg-red-700 text-white text-sm font-medium rounded-md hover:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors">
					<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 10h6v4H9z"></path>
					</svg>
					停止
				</button>
			} else {
				<button 
					data-project={ project.ID }
					data-action="start"
					class="flex items-center justify-center px-3 py-2 bg-green-600 dark:bg-green-700 text-white text-sm font-medium rounded-md hover:bg-green-700 dark:hover:bg-green-800 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors">
					<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4V8a3 3 0 016 0v2M7 16h10a2 2 0 002-2V8a2 2 0 00-2-2H7a2 2 0 00-2 2v6a2 2 0 002 2z"></path>
					</svg>
					启动
				</button>
			}
			
			<!-- Restart Button -->
			<button 
				data-project={ project.ID }
				data-action="restart"
				class="flex items-center justify-center px-3 py-2 bg-orange-600 dark:bg-orange-700 text-white text-sm font-medium rounded-md hover:bg-orange-700 dark:hover:bg-orange-800 focus:outline-none focus:ring-2 focus:ring-orange-500 transition-colors">
				<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
				</svg>
				重启
			</button>
			
			<!-- Configure Button -->
			<button 
				data-project={ project.ID }
				data-action="configure"
				class="flex items-center justify-center px-3 py-2 bg-blue-600 dark:bg-blue-700 text-white text-sm font-medium rounded-md hover:bg-blue-700 dark:hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
				<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
				</svg>
				配置
			</button>
			
			<!-- File Manager Button -->
			<button 
				data-project={ project.ID }
				data-action="files"
				class="flex items-center justify-center px-3 py-2 bg-purple-600 dark:bg-purple-700 text-white text-sm font-medium rounded-md hover:bg-purple-700 dark:hover:bg-purple-800 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-colors">
				<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-5l-2-2H5a2 2 0 00-2 2z"></path>
				</svg>
				文件
			</button>
		</div>
		
		<!-- Additional Actions Row -->
		<div class="flex space-x-2 mt-2">
			<button 
				data-project={ project.ID }
				data-action="backup"
				class="flex-1 flex items-center justify-center px-3 py-2 bg-gray-600 dark:bg-gray-700 text-white text-sm font-medium rounded-md hover:bg-gray-700 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
				<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
				</svg>
				备份
			</button>
			<button 
				data-project={ project.ID }
				data-action="delete"
				class="flex items-center justify-center px-3 py-2 bg-red-600 dark:bg-red-700 text-white text-sm font-medium rounded-md hover:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors">
				<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1v3M4 7h16"></path>
				</svg>
			</button>
		</div>
	</div>

	<script>
		// Use event delegation for project actions
		document.addEventListener('click', function(e) {
			if (e.target.dataset.project && e.target.dataset.action) {
				e.preventDefault();
				const projectId = e.target.dataset.project;
				const action = e.target.dataset.action;
				
				switch(action) {
					case 'start':
						startProject(projectId);
						break;
					case 'stop':
						stopProject(projectId);
						break;
					case 'restart':
						restartProject(projectId);
						break;
					case 'configure':
						openConfigModal(projectId);
						break;
					case 'files':
						window.location.href = `/projects/${projectId}/files`;
						break;
					case 'backup':
						window.location.href = `/projects/${projectId}/backup`;
						break;
					case 'delete':
						deleteProject(projectId);
						break;
				}
			}
		});

		// Open configuration modal
		function openConfigModal(projectId) {
			// Get project data first
			fetch(`/api/projects/${projectId}`)
				.then(response => {
					if (!response.ok) {
						throw new Error(`HTTP error! status: ${response.status}`);
					}
					return response.json();
				})
				.then(result => {
					if (!result.success) {
						throw new Error(result.error || '获取项目信息失败');
					}
					
					const project = result.data;
					// Populate modal with project data
					document.getElementById('config-project-id').value = projectId;
					document.getElementById('config-project-name').textContent = project.Name || project.name || 'Unknown Project';
					document.getElementById('config-domain').value = project.Domain || project.domain || '';
					document.getElementById('config-aliases').value = project.Aliases ? (Array.isArray(project.Aliases) ? project.Aliases.join('\n') : project.Aliases) : '';
					document.getElementById('config-document-root').value = project.Path || project.path || '/var/www/html';
					document.getElementById('config-ssl-enabled').checked = project.SSLEnabled || project.sslEnabled || false;
					document.getElementById('config-ssl-method').value = project.SSLMethod || project.sslMethod || 'letsencrypt';
					document.getElementById('config-php-enabled').checked = project.PHPEnabled || project.phpEnabled || false;
					document.getElementById('config-php-version').value = project.PHPVersion || project.phpVersion || '8.3';
					document.getElementById('config-index-files').value = project.IndexFiles || project.indexFiles || 'index.html index.php';
					document.getElementById('config-custom-nginx').value = project.CustomNginx || project.customNginx || '';
					
					// Show modal
					document.getElementById('config-modal').classList.remove('hidden');
					
					// Update preview
					updateNginxPreview();
				})
				.catch(error => {
					console.error('Error loading project:', error);
					alert('加载项目信息失败: ' + error.message);
				});
		}
		
		// Project control functions
		function startProject(projectId) {
			fetch(`/api/projects/${projectId}/start`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
			})
			.then(response => {
				if (response.ok) {
					location.reload();
				} else {
					alert('启动项目失败');
				}
			})
			.catch(error => {
				console.error('Error:', error);
				alert('启动项目失败');
			});
		}
		
		function stopProject(projectId) {
			if (confirm('确定要停止这个项目吗？')) {
				fetch(`/api/projects/${projectId}/stop`, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
				})
				.then(response => {
					if (response.ok) {
						location.reload();
					} else {
						alert('停止项目失败');
					}
				})
				.catch(error => {
					console.error('Error:', error);
					alert('停止项目失败');
				});
			}
		}
		
		function restartProject(projectId) {
			if (confirm('确定要重启这个项目吗？')) {
				fetch(`/api/projects/${projectId}/restart`, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
				})
				.then(response => {
					if (response.ok) {
						location.reload();
					} else {
						alert('重启项目失败');
					}
				})
				.catch(error => {
					console.error('Error:', error);
					alert('重启项目失败');
				});
			}
		}
		
		// Delete project function
		function deleteProject(projectId) {
			if (confirm('确定要删除这个项目吗？此操作无法撤销。')) {
				fetch(`/api/projects/${projectId}/delete`, {
					method: 'DELETE',
					headers: {
						'Content-Type': 'application/json',
					},
				})
				.then(response => response.json())
				.then(result => {
					if (result.success) {
						alert('项目删除成功');
						location.reload();
					} else {
						alert('删除项目失败: ' + (result.error || '未知错误'));
					}
				})
				.catch(error => {
					console.error('Error:', error);
					alert('删除项目失败: 网络错误');
				});
			}
		}
	</script>
}
