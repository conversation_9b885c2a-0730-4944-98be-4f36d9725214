package components

import "server-panel/internal/i18n"

templ ProjectConfigModal(lang string) {
	<!-- Project Configuration Modal -->
	<div id="config-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
		<div class="relative top-20 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white dark:bg-gray-800">
			<!-- Modal Header -->
			<div class="flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700">
				<h3 class="text-lg font-medium text-gray-900 dark:text-white">
					{ i18n.T(lang, "projects.config.title") }: <span id="config-project-name" class="text-blue-600 dark:text-blue-400"></span>
				</h3>
				<button onclick="closeConfigModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
					<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
					</svg>
				</button>
			</div>

			<!-- Modal Content -->
			<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
				<!-- Configuration Form -->
				<div class="lg:col-span-2 space-y-6">
					<input type="hidden" id="config-project-id" />
					
					<!-- Basic Settings -->
					<div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
						<h4 class="text-md font-medium text-gray-900 dark:text-white mb-4">{ i18n.T(lang, "projects.config.basic.title") }</h4>
						<div class="space-y-4">
							<div>
								<label for="config-domain" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
									{ i18n.T(lang, "projects.config.basic.domain") }
								</label>
								<input type="text" id="config-domain" oninput="updateNginxPreview()"
									class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
									placeholder="example.com">
							</div>
							<div>
								<label for="config-aliases" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
									{ i18n.T(lang, "projects.config.basic.aliases") }
								</label>
								<textarea id="config-aliases" rows="3" oninput="updateNginxPreview()"
									class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
									placeholder="www.example.com&#10;subdomain.example.com"></textarea>
								<p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{ i18n.T(lang, "projects.config.basic.aliases.help") }</p>
							</div>
							<div>
								<label for="config-document-root" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
									{ i18n.T(lang, "projects.config.basic.documentRoot") }
								</label>
								<input type="text" id="config-document-root" oninput="updateNginxPreview()"
									class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
									value="/var/www/html">
							</div>
						</div>
					</div>

					<!-- SSL Settings -->
					<div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
						<h4 class="text-md font-medium text-gray-900 dark:text-white mb-4">{ i18n.T(lang, "projects.config.ssl.title") }</h4>
						<div class="space-y-4">
							<div class="flex items-center">
								<input type="checkbox" id="config-ssl-enabled" onchange="updateNginxPreview()"
									class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
								<label for="config-ssl-enabled" class="ml-2 block text-sm text-gray-900 dark:text-white">
									{ i18n.T(lang, "projects.config.ssl.enable") }
								</label>
							</div>
							<div id="ssl-method-section" style="display: none;">
								<label for="config-ssl-method" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
									{ i18n.T(lang, "projects.config.ssl.method") }
								</label>
								<select id="config-ssl-method" onchange="updateNginxPreview()"
									class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white">
									<option value="letsencrypt">{ i18n.T(lang, "projects.config.ssl.letsencrypt") }</option>
									<option value="custom">{ i18n.T(lang, "projects.config.ssl.custom") }</option>
								</select>
							</div>
						</div>
					</div>

					<!-- Advanced Settings -->
					<div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
						<h4 class="text-md font-medium text-gray-900 dark:text-white mb-4">{ i18n.T(lang, "projects.config.advanced.title") }</h4>
						<div class="space-y-4">
							<div class="flex items-center">
								<input type="checkbox" id="config-php-enabled" onchange="updateNginxPreview()"
									class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
								<label for="config-php-enabled" class="ml-2 block text-sm text-gray-900 dark:text-white">
									{ i18n.T(lang, "projects.config.advanced.phpEnable") }
								</label>
							</div>
							<div x-show="document.getElementById('config-php-enabled').checked">
								<label for="config-php-version" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
									{ i18n.T(lang, "projects.config.advanced.phpVersion") }
								</label>
								<select id="config-php-version" onchange="updateNginxPreview()"
									class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white">
									<option value="8.3">PHP 8.3</option>
									<option value="8.2">PHP 8.2</option>
									<option value="8.1">PHP 8.1</option>
									<option value="7.4">PHP 7.4</option>
								</select>
							</div>
							<div>
								<label for="config-index-files" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
									{ i18n.T(lang, "projects.config.advanced.indexFiles") }
								</label>
								<input type="text" id="config-index-files" oninput="updateNginxPreview()"
									class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
									value="index.html index.php">
							</div>
							<div>
								<label for="config-custom-nginx" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
									{ i18n.T(lang, "projects.config.advanced.customNginx") }
								</label>
								<textarea id="config-custom-nginx" rows="4" oninput="updateNginxPreview()"
									class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white font-mono text-sm"
									placeholder="# Custom Nginx directives"></textarea>
							</div>
						</div>
					</div>
				</div>

				<!-- Preview and Actions -->
				<div class="space-y-6">
					<!-- Configuration Preview -->
					<div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
						<h4 class="text-md font-medium text-gray-900 dark:text-white mb-4">{ i18n.T(lang, "projects.config.preview.title") }</h4>
						<pre id="nginx-preview" class="bg-gray-900 text-green-400 p-4 rounded text-xs overflow-auto max-h-96 font-mono whitespace-pre-wrap"></pre>
					</div>

					<!-- Quick Actions -->
					<div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
						<h4 class="text-md font-medium text-gray-900 dark:text-white mb-4">{ i18n.T(lang, "projects.config.actions.title") }</h4>
						<div class="space-y-3">
							<button onclick="saveConfig()"
								class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
								{ i18n.T(lang, "projects.config.actions.save") }
							</button>
							<button onclick="testConfig()"
								class="w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors">
								{ i18n.T(lang, "projects.config.actions.test") }
							</button>
							<button onclick="reloadNginx()"
								class="w-full bg-orange-600 text-white px-4 py-2 rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 transition-colors">
								{ i18n.T(lang, "projects.config.actions.reload") }
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<script>
		// Modal control functions
		function closeConfigModal() {
			document.getElementById('config-modal').classList.add('hidden');
		}
		
		function saveConfig() {
			const projectId = document.getElementById('config-project-id').value;
			const config = {
				domain: document.getElementById('config-domain').value,
				aliases: document.getElementById('config-aliases').value.split('\n').filter(a => a.trim()),
				documentRoot: document.getElementById('config-document-root').value,
				sslEnabled: document.getElementById('config-ssl-enabled').checked,
				sslMethod: document.getElementById('config-ssl-method').value,
				phpEnabled: document.getElementById('config-php-enabled').checked,
				phpVersion: document.getElementById('config-php-version').value,
				indexFiles: document.getElementById('config-index-files').value,
				customNginx: document.getElementById('config-custom-nginx').value
			};
			
			fetch(`/api/projects/${projectId}/config`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(config)
			})
			.then(response => {
				if (response.ok) {
					alert('配置保存成功');
					closeConfigModal();
					location.reload();
				} else {
					alert('配置保存失败');
				}
			})
			.catch(error => {
				console.error('Error:', error);
				alert('配置保存失败');
			});
		}
		
		function testConfig() {
			const projectId = document.getElementById('config-project-id').value;
			fetch(`/api/projects/${projectId}/test-config`, {
				method: 'POST'
			})
			.then(response => response.json())
			.then(data => {
				if (data.success) {
					alert('配置测试通过');
				} else {
					alert('配置测试失败: ' + data.error);
				}
			})
			.catch(error => {
				console.error('Error:', error);
				alert('配置测试失败');
			});
		}
		
		function reloadNginx() {
			fetch('/api/nginx/reload', {
				method: 'POST'
			})
			.then(response => {
				if (response.ok) {
					alert('Nginx重载成功');
				} else {
					alert('Nginx重载失败');
				}
			})
			.catch(error => {
				console.error('Error:', error);
				alert('Nginx重载失败');
			});
		}
		
		function updateNginxPreview() {
			const domain = document.getElementById('config-domain').value || 'example.com';
			const aliases = document.getElementById('config-aliases').value.split('\n').filter(a => a.trim());
			const documentRoot = document.getElementById('config-document-root').value || '/var/www/html';
			const sslEnabled = document.getElementById('config-ssl-enabled').checked;
			
			// Show/hide SSL method section
			const sslMethodSection = document.getElementById('ssl-method-section');
			if (sslEnabled) {
				sslMethodSection.style.display = 'block';
			} else {
				sslMethodSection.style.display = 'none';
			}
			const phpEnabled = document.getElementById('config-php-enabled').checked;
			const phpVersion = document.getElementById('config-php-version').value;
			const indexFiles = document.getElementById('config-index-files').value || 'index.html index.php';
			const customNginx = document.getElementById('config-custom-nginx').value;
			
			let config = `server {\n`;
			config += `    listen 80;\n`;
			config += `    server_name ${domain}`;
			if (aliases.length > 0) {
				config += ` ${aliases.join(' ')}`;
			}
			config += `;\n`;
			config += `    root ${documentRoot};\n`;
			config += `    index ${indexFiles};\n\n`;
			
			if (sslEnabled) {
				config += `    # Redirect HTTP to HTTPS\n`;
				config += `    return 301 https://$server_name$request_uri;\n`;
				config += `}\n\n`;
				config += `server {\n`;
				config += `    listen 443 ssl http2;\n`;
				config += `    server_name ${domain}`;
				if (aliases.length > 0) {
					config += ` ${aliases.join(' ')}`;
				}
				config += `;\n`;
				config += `    root ${documentRoot};\n`;
				config += `    index ${indexFiles};\n\n`;
				config += `    ssl_certificate /etc/ssl/certs/${domain}.crt;\n`;
				config += `    ssl_certificate_key /etc/ssl/private/${domain}.key;\n\n`;
			}
			
			if (phpEnabled) {
				config += `    location ~ \\.php$ {\n`;
				config += `        try_files $uri =404;\n`;
				config += `        fastcgi_split_path_info ^(.+\\.php)(/.+)$;\n`;
				config += `        fastcgi_pass unix:/var/run/php/php${phpVersion}-fpm.sock;\n`;
				config += `        fastcgi_index index.php;\n`;
				config += `        include fastcgi_params;\n`;
				config += `        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;\n`;
				config += `    }\n\n`;
			}
			
			config += `    location / {\n`;
			config += `        try_files $uri $uri/ =404;\n`;
			config += `    }\n`;
			
			if (customNginx.trim()) {
				config += `\n    # Custom Configuration\n`;
				config += `    ${customNginx.replace(/\n/g, '\n    ')}\n`;
			}
			
			config += `}`;
			
			document.getElementById('nginx-preview').textContent = config;
		}
	</script>
}
