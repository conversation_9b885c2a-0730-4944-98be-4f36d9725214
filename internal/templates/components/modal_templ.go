// Code generated by templ - DO NOT EDIT.

// templ: version: v0.3.924
package components

//lint:file-ignore SA4006 This context is only used if a nested component is present.

import "github.com/a-h/templ"
import templruntime "github.com/a-h/templ/runtime"

func Modal() templ.Component {
	return templruntime.GeneratedTemplate(func(templ_7745c5c3_Input templruntime.GeneratedComponentInput) (templ_7745c5c3_Err error) {
		templ_7745c5c3_W, ctx := templ_7745c5c3_Input.Writer, templ_7745c5c3_Input.Context
		if templ_7745c5c3_CtxErr := ctx.Err(); templ_7745c5c3_CtxErr != nil {
			return templ_7745c5c3_CtxErr
		}
		templ_7745c5c3_Buffer, templ_7745c5c3_IsBuffer := templruntime.GetBuffer(templ_7745c5c3_W)
		if !templ_7745c5c3_IsBuffer {
			defer func() {
				templ_7745c5c3_BufErr := templruntime.ReleaseBuffer(templ_7745c5c3_Buffer)
				if templ_7745c5c3_Err == nil {
					templ_7745c5c3_Err = templ_7745c5c3_BufErr
				}
			}()
		}
		ctx = templ.InitializeContext(ctx)
		templ_7745c5c3_Var1 := templ.GetChildren(ctx)
		if templ_7745c5c3_Var1 == nil {
			templ_7745c5c3_Var1 = templ.NopComponent
		}
		ctx = templ.ClearChildren(ctx)
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 1, "<!-- 简化的模态框容器 - 使用纯 CSS 和 JavaScript --><div id=\"modal\" class=\"fixed inset-0 z-50 overflow-y-auto hidden transition-opacity duration-300\"><div class=\"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\"><!-- 背景遮罩 --><div class=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\" onclick=\"closeModal()\"></div><span class=\"hidden sm:inline-block sm:align-middle sm:h-screen\">&#8203;</span><!-- 模态框内容 --><div class=\"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\"><div class=\"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4\"><div class=\"sm:flex sm:items-start\"><div class=\"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full\"><div class=\"flex justify-between items-center mb-4\"><h3 id=\"modal-title\" class=\"text-lg leading-6 font-medium text-gray-900 dark:text-gray-100\">模态框标题</h3><button onclick=\"closeModal()\" class=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"><svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path></svg></button></div><div id=\"modal-content\"><!-- 动态内容 --></div></div></div></div></div></div></div><!-- 简化的模态框控制脚本 --><script>\n\t\tfunction openModal(title = '') {\n\t\t\tconst modal = document.getElementById('modal');\n\t\t\tconst titleEl = document.getElementById('modal-title');\n\t\t\tif (modal) {\n\t\t\t\tif (titleEl && title) titleEl.textContent = title;\n\t\t\t\tmodal.classList.remove('hidden');\n\t\t\t\tdocument.body.style.overflow = 'hidden'; // 防止背景滚动\n\t\t\t}\n\t\t}\n\n\t\tfunction closeModal() {\n\t\t\tconst modal = document.getElementById('modal');\n\t\t\tif (modal) {\n\t\t\t\tmodal.classList.add('hidden');\n\t\t\t\tdocument.body.style.overflow = ''; // 恢复滚动\n\t\t\t}\n\t\t}\n\n\t\t// ESC 键关闭模态框\n\t\tdocument.addEventListener('keydown', function(e) {\n\t\t\tif (e.key === 'Escape') {\n\t\t\t\tcloseModal();\n\t\t\t}\n\t\t});\n\t</script>")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		return nil
	})
}

var _ = templruntime.GeneratedTemplate
