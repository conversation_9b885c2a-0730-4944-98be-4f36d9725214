package components

import "server-panel/internal/i18n"

templ DiskCard(currentLang string) {
	<!-- 磁盘卡片 -->
	<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
		<div class="flex items-start justify-between">
			<div class="flex-1 pr-4">
				<div class="flex items-center justify-between mb-2">
					<p class="text-sm font-medium text-gray-600 dark:text-gray-400">{ i18n.T(currentLang, "system.disk.usage") }</p>
					<div class="w-10 h-10 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center ml-3">
						<svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
						</svg>
					</div>
				</div>
				<p id="disk-usage" class="text-2xl font-bold text-yellow-600 dark:text-yellow-400 mb-3">--</p>
				<div class="space-y-1">
					<div class="flex justify-between text-xs text-gray-500 dark:text-gray-400">
						<span>{ i18n.T(currentLang, "system.disk.used") }</span>
						<span id="disk-used" class="font-medium">--</span>
					</div>
					<div class="flex justify-between text-xs text-gray-500 dark:text-gray-400">
						<span>{ i18n.T(currentLang, "system.disk.total") }</span>
						<span id="disk-total" class="font-medium">--</span>
					</div>
				</div>
			</div>
		</div>
		<div class="mt-4">
			<div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
				<div id="disk-progress" class="bg-yellow-600 dark:bg-yellow-500 h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
			</div>
		</div>
	</div>
}
