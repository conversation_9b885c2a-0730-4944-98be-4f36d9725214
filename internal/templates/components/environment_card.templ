package components

import (
	"fmt"
	"server-panel/internal/environment"
	"server-panel/internal/i18n"
	"strconv"
)

templ EnvironmentCard(service environment.Service, currentLang string) {
	<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow h-full flex flex-col">
		<div class="flex items-start justify-between mb-4">
			<div class="flex items-center flex-1">
				<div class="text-2xl mr-3 flex-shrink-0">{ service.Icon }</div>
				<div class="min-w-0 flex-1">
					<h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 truncate">{ service.DisplayName }</h3>
					<p class="text-sm text-gray-500 dark:text-gray-400 line-clamp-2">{ service.Description }</p>
				</div>
			</div>
			<div class="flex flex-col items-end ml-4 flex-shrink-0">
				if service.Status == environment.StatusInstalled {
					<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
						<svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
						</svg>
						{ i18n.T(currentLang, "environment.status.installed") }
					</span>
				} else if service.Status == environment.StatusInstalling {
					<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200">
						<svg class="animate-spin w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24">
							<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
							<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
						</svg>
						{ i18n.T(currentLang, "environment.status.installing") }
					</span>
				} else {
					<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
						{ i18n.T(currentLang, "environment.status.not_installed") }
					</span>
				}
			</div>
		</div>

		<div class="flex-1">
			if service.Status == environment.StatusInstalled {
				<div class="space-y-2">
					<div class="flex justify-between text-sm">
						<span class="text-gray-500 dark:text-gray-400">{ i18n.T(currentLang, "environment.version") }:</span>
						<span class="text-gray-900 dark:text-gray-100">{ service.Version }</span>
					</div>
					if service.Port > 0 {
						<div class="flex justify-between text-sm">
							<span class="text-gray-500 dark:text-gray-400">{ i18n.T(currentLang, "environment.port") }:</span>
							<span class="text-gray-900 dark:text-gray-100">{ strconv.Itoa(service.Port) }</span>
						</div>
					}
					<div class="flex justify-between text-sm">
						<span class="text-gray-500 dark:text-gray-400">{ i18n.T(currentLang, "environment.status") }:</span>
						if service.IsRunning {
							<span class="text-green-600 dark:text-green-400 font-medium">{ i18n.T(currentLang, "environment.status.running") }</span>
						} else {
							<span class="text-red-600 dark:text-red-400 font-medium">{ i18n.T(currentLang, "environment.status.stopped") }</span>
						}
					</div>
				</div>
			}
		</div>

		<div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
			if service.Status == environment.StatusInstalled {
				<div class="flex flex-wrap gap-2">
					if !service.IsRunning {
						<button @click={ fmt.Sprintf("startService('%s')", service.Name) } class="flex-1 bg-green-600 dark:bg-green-700 text-white px-3 py-2 rounded-md text-xs font-medium hover:bg-green-700 dark:hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 min-w-[60px]">
							{ i18n.T(currentLang, "environment.action.start") }
						</button>
					} else {
						<button @click={ fmt.Sprintf("stopService('%s')", service.Name) } class="flex-1 bg-red-600 dark:bg-red-700 text-white px-3 py-2 rounded-md text-xs font-medium hover:bg-red-700 dark:hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 min-w-[60px]">
							{ i18n.T(currentLang, "environment.action.stop") }
						</button>
					}
					<button @click={ fmt.Sprintf("restartService('%s')", service.Name) } class="flex-1 bg-blue-600 dark:bg-blue-700 text-white px-3 py-2 rounded-md text-xs font-medium hover:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[60px]">
						{ i18n.T(currentLang, "environment.action.restart") }
					</button>
					<button @click={ fmt.Sprintf("uninstallService('%s')", service.Name) } class="flex-1 bg-gray-600 dark:bg-gray-700 text-white px-3 py-2 rounded-md text-xs font-medium hover:bg-gray-700 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 min-w-[60px]">
						{ i18n.T(currentLang, "environment.action.uninstall") }
					</button>
					<button @click={ fmt.Sprintf("openSettingsModal('%s')", service.Name) } class="flex-1 bg-gray-500 dark:bg-gray-600 text-white px-3 py-2 rounded-md text-xs font-medium hover:bg-gray-600 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-400 min-w-[60px]">
						{ i18n.T(currentLang, "environment.action.settings") }
					</button>
				</div>
			} else {
				<button
					class="w-full bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
					data-service={ service.Name }
					data-action="install">
					{ i18n.T(currentLang, "environment.action.install") }
				</button>
			}
		</div>
	</div>
}
