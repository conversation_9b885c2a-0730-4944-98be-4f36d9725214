package components

import "server-panel/internal/i18n"

// ProjectCreateForm 项目创建表单组件
templ ProjectCreateForm(currentLang string) {
	<form hx-post="/api/projects/create" hx-target="body" hx-swap="outerHTML">
		<div class="mb-4">
			<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
				{ i18n.T(currentLang, "projects.project_name") }
			</label>
			<input
				type="text"
				name="name"
				id="project-name-input"
				class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
				placeholder="my-awesome-project"
				oninput="updateProjectPath()"
				required>
			<p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
				<svg class="inline w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z"></path>
				</svg>
				项目将创建在: <span class="font-mono text-blue-600 dark:text-blue-400" id="project-path-display">/var/www/[项目名称]</span>
			</p>
		</div>
		
		<div class="mb-4">
			<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
				{ i18n.T(currentLang, "projects.domain") }
			</label>
			<input
				type="text"
				name="domain"
				class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
				placeholder="example.com">
		</div>
		
		<div class="mb-4 space-y-2">
			<label class="flex items-center">
				<input type="checkbox" name="createDB" value="true" checked class="mr-2 text-blue-600 dark:text-blue-400">
				<span class="text-sm text-gray-700 dark:text-gray-300">
					{ i18n.T(currentLang, "projects.create_database") }
				</span>
			</label>
			<label class="flex items-center">
				<input type="checkbox" name="enableSSL" value="true" class="mr-2 text-blue-600 dark:text-blue-400">
				<span class="text-sm text-gray-700 dark:text-gray-300">
					{ i18n.T(currentLang, "projects.enable_ssl") }
				</span>
			</label>
			<label class="flex items-center">
				<input type="checkbox" name="enableBackup" value="true" class="mr-2 text-blue-600 dark:text-blue-400">
				<span class="text-sm text-gray-700 dark:text-gray-300">
					{ i18n.T(currentLang, "projects.enable_backup") }
				</span>
			</label>
		</div>
		
		<div class="flex space-x-3">
			<button
				type="submit"
				class="flex-1 bg-blue-500 dark:bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-300">
				{ i18n.T(currentLang, "projects.create_project") }
			</button>
			<button
				type="button"
				onclick="closeModal()"
				class="flex-1 bg-gray-300 dark:bg-gray-600 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md hover:bg-gray-400 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-300">
				{ i18n.T(currentLang, "common.cancel") }
			</button>
		</div>
	</form>

	<script>
		let projectsDir = '/var/www'; // 默认值

		// 获取实际的项目目录
		fetch('/api/projects/config')
			.then(response => response.json())
			.then(data => {
				if (data.success) {
					projectsDir = data.data.projects_dir;
					updateProjectPath(); // 更新显示
				}
			})
			.catch(error => {
				console.error('Failed to load projects config:', error);
			});

		function updateProjectPath() {
			const nameInput = document.getElementById('project-name-input');
			const pathDisplay = document.getElementById('project-path-display');
			if (nameInput && pathDisplay) {
				const projectName = nameInput.value.trim();
				pathDisplay.textContent = `${projectsDir}/${projectName || '[项目名称]'}`;
			}
		}
	</script>
}
