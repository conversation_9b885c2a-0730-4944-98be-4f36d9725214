// Code generated by templ - DO NOT EDIT.

// templ: version: v0.3.924
package components

//lint:file-ignore SA4006 This context is only used if a nested component is present.

import "github.com/a-h/templ"
import templruntime "github.com/a-h/templ/runtime"

import "server-panel/internal/i18n"

func ProjectConfigModal(lang string) templ.Component {
	return templruntime.GeneratedTemplate(func(templ_7745c5c3_Input templruntime.GeneratedComponentInput) (templ_7745c5c3_Err error) {
		templ_7745c5c3_W, ctx := templ_7745c5c3_Input.Writer, templ_7745c5c3_Input.Context
		if templ_7745c5c3_CtxErr := ctx.Err(); templ_7745c5c3_CtxErr != nil {
			return templ_7745c5c3_CtxErr
		}
		templ_7745c5c3_Buffer, templ_7745c5c3_IsBuffer := templruntime.GetBuffer(templ_7745c5c3_W)
		if !templ_7745c5c3_IsBuffer {
			defer func() {
				templ_7745c5c3_BufErr := templruntime.ReleaseBuffer(templ_7745c5c3_Buffer)
				if templ_7745c5c3_Err == nil {
					templ_7745c5c3_Err = templ_7745c5c3_BufErr
				}
			}()
		}
		ctx = templ.InitializeContext(ctx)
		templ_7745c5c3_Var1 := templ.GetChildren(ctx)
		if templ_7745c5c3_Var1 == nil {
			templ_7745c5c3_Var1 = templ.NopComponent
		}
		ctx = templ.ClearChildren(ctx)
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 1, "<!-- Project Configuration Modal --><div id=\"config-modal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden\"><div class=\"relative top-20 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white dark:bg-gray-800\"><!-- Modal Header --><div class=\"flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700\"><h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var2 string
		templ_7745c5c3_Var2, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(lang, "projects.config.title"))
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/project_config_modal.templ`, Line: 12, Col: 44}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var2))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 2, ": <span id=\"config-project-name\" class=\"text-blue-600 dark:text-blue-400\"></span></h3><button onclick=\"closeConfigModal()\" class=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"><svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path></svg></button></div><!-- Modal Content --><div class=\"grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6\"><!-- Configuration Form --><div class=\"lg:col-span-2 space-y-6\"><input type=\"hidden\" id=\"config-project-id\"><!-- Basic Settings --><div class=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\"><h4 class=\"text-md font-medium text-gray-900 dark:text-white mb-4\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var3 string
		templ_7745c5c3_Var3, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(lang, "projects.config.basic.title"))
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/project_config_modal.templ`, Line: 29, Col: 118}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var3))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 3, "</h4><div class=\"space-y-4\"><div><label for=\"config-domain\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var4 string
		templ_7745c5c3_Var4, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(lang, "projects.config.basic.domain"))
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/project_config_modal.templ`, Line: 33, Col: 55}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var4))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 4, "</label> <input type=\"text\" id=\"config-domain\" oninput=\"updateNginxPreview()\" class=\"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white\" placeholder=\"example.com\"></div><div><label for=\"config-aliases\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var5 string
		templ_7745c5c3_Var5, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(lang, "projects.config.basic.aliases"))
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/project_config_modal.templ`, Line: 41, Col: 56}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var5))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 5, "</label> <textarea id=\"config-aliases\" rows=\"3\" oninput=\"updateNginxPreview()\" class=\"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white\" placeholder=\"www.example.com&#10;subdomain.example.com\"></textarea><p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var6 string
		templ_7745c5c3_Var6, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(lang, "projects.config.basic.aliases.help"))
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/project_config_modal.templ`, Line: 46, Col: 117}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var6))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 6, "</p></div><div><label for=\"config-document-root\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var7 string
		templ_7745c5c3_Var7, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(lang, "projects.config.basic.documentRoot"))
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/project_config_modal.templ`, Line: 50, Col: 61}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var7))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 7, "</label> <input type=\"text\" id=\"config-document-root\" oninput=\"updateNginxPreview()\" class=\"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white\" value=\"/var/www/html\"></div></div></div><!-- SSL Settings --><div class=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\"><h4 class=\"text-md font-medium text-gray-900 dark:text-white mb-4\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var8 string
		templ_7745c5c3_Var8, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(lang, "projects.config.ssl.title"))
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/project_config_modal.templ`, Line: 61, Col: 116}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var8))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 8, "</h4><div class=\"space-y-4\"><div class=\"flex items-center\"><input type=\"checkbox\" id=\"config-ssl-enabled\" onchange=\"updateNginxPreview()\" class=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"> <label for=\"config-ssl-enabled\" class=\"ml-2 block text-sm text-gray-900 dark:text-white\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var9 string
		templ_7745c5c3_Var9, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(lang, "projects.config.ssl.enable"))
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/project_config_modal.templ`, Line: 67, Col: 53}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var9))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 9, "</label></div><div id=\"ssl-method-section\" style=\"display: none;\"><label for=\"config-ssl-method\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var10 string
		templ_7745c5c3_Var10, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(lang, "projects.config.ssl.method"))
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/project_config_modal.templ`, Line: 72, Col: 53}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var10))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 10, "</label> <select id=\"config-ssl-method\" onchange=\"updateNginxPreview()\" class=\"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white\"><option value=\"letsencrypt\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var11 string
		templ_7745c5c3_Var11, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(lang, "projects.config.ssl.letsencrypt"))
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/project_config_modal.templ`, Line: 76, Col: 86}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var11))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 11, "</option> <option value=\"custom\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var12 string
		templ_7745c5c3_Var12, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(lang, "projects.config.ssl.custom"))
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/project_config_modal.templ`, Line: 77, Col: 76}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var12))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 12, "</option></select></div></div></div><!-- Advanced Settings --><div class=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\"><h4 class=\"text-md font-medium text-gray-900 dark:text-white mb-4\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var13 string
		templ_7745c5c3_Var13, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(lang, "projects.config.advanced.title"))
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/project_config_modal.templ`, Line: 85, Col: 121}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var13))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 13, "</h4><div class=\"space-y-4\"><div class=\"flex items-center\"><input type=\"checkbox\" id=\"config-php-enabled\" onchange=\"updateNginxPreview()\" class=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"> <label for=\"config-php-enabled\" class=\"ml-2 block text-sm text-gray-900 dark:text-white\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var14 string
		templ_7745c5c3_Var14, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(lang, "projects.config.advanced.phpEnable"))
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/project_config_modal.templ`, Line: 91, Col: 61}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var14))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 14, "</label></div><div x-show=\"document.getElementById('config-php-enabled').checked\"><label for=\"config-php-version\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var15 string
		templ_7745c5c3_Var15, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(lang, "projects.config.advanced.phpVersion"))
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/project_config_modal.templ`, Line: 96, Col: 62}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var15))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 15, "</label> <select id=\"config-php-version\" onchange=\"updateNginxPreview()\" class=\"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white\"><option value=\"8.3\">PHP 8.3</option> <option value=\"8.2\">PHP 8.2</option> <option value=\"8.1\">PHP 8.1</option> <option value=\"7.4\">PHP 7.4</option></select></div><div><label for=\"config-index-files\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var16 string
		templ_7745c5c3_Var16, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(lang, "projects.config.advanced.indexFiles"))
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/project_config_modal.templ`, Line: 108, Col: 62}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var16))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 16, "</label> <input type=\"text\" id=\"config-index-files\" oninput=\"updateNginxPreview()\" class=\"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white\" value=\"index.html index.php\"></div><div><label for=\"config-custom-nginx\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var17 string
		templ_7745c5c3_Var17, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(lang, "projects.config.advanced.customNginx"))
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/project_config_modal.templ`, Line: 116, Col: 63}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var17))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 17, "</label> <textarea id=\"config-custom-nginx\" rows=\"4\" oninput=\"updateNginxPreview()\" class=\"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white font-mono text-sm\" placeholder=\"# Custom Nginx directives\"></textarea></div></div></div></div><!-- Preview and Actions --><div class=\"space-y-6\"><!-- Configuration Preview --><div class=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\"><h4 class=\"text-md font-medium text-gray-900 dark:text-white mb-4\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var18 string
		templ_7745c5c3_Var18, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(lang, "projects.config.preview.title"))
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/project_config_modal.templ`, Line: 130, Col: 120}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var18))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 18, "</h4><pre id=\"nginx-preview\" class=\"bg-gray-900 text-green-400 p-4 rounded text-xs overflow-auto max-h-96 font-mono whitespace-pre-wrap\"></pre></div><!-- Quick Actions --><div class=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\"><h4 class=\"text-md font-medium text-gray-900 dark:text-white mb-4\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var19 string
		templ_7745c5c3_Var19, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(lang, "projects.config.actions.title"))
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/project_config_modal.templ`, Line: 136, Col: 120}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var19))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 19, "</h4><div class=\"space-y-3\"><button onclick=\"saveConfig()\" class=\"w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var20 string
		templ_7745c5c3_Var20, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(lang, "projects.config.actions.save"))
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/project_config_modal.templ`, Line: 140, Col: 54}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var20))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 20, "</button> <button onclick=\"testConfig()\" class=\"w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var21 string
		templ_7745c5c3_Var21, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(lang, "projects.config.actions.test"))
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/project_config_modal.templ`, Line: 144, Col: 54}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var21))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 21, "</button> <button onclick=\"reloadNginx()\" class=\"w-full bg-orange-600 text-white px-4 py-2 rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 transition-colors\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var22 string
		templ_7745c5c3_Var22, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(lang, "projects.config.actions.reload"))
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/project_config_modal.templ`, Line: 148, Col: 56}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var22))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 22, "</button></div></div></div></div></div></div><script>\n\t\t// Modal control functions\n\t\tfunction closeConfigModal() {\n\t\t\tdocument.getElementById('config-modal').classList.add('hidden');\n\t\t}\n\t\t\n\t\tfunction saveConfig() {\n\t\t\tconst projectId = document.getElementById('config-project-id').value;\n\t\t\tconst config = {\n\t\t\t\tdomain: document.getElementById('config-domain').value,\n\t\t\t\taliases: document.getElementById('config-aliases').value.split('\\n').filter(a => a.trim()),\n\t\t\t\tdocumentRoot: document.getElementById('config-document-root').value,\n\t\t\t\tsslEnabled: document.getElementById('config-ssl-enabled').checked,\n\t\t\t\tsslMethod: document.getElementById('config-ssl-method').value,\n\t\t\t\tphpEnabled: document.getElementById('config-php-enabled').checked,\n\t\t\t\tphpVersion: document.getElementById('config-php-version').value,\n\t\t\t\tindexFiles: document.getElementById('config-index-files').value,\n\t\t\t\tcustomNginx: document.getElementById('config-custom-nginx').value\n\t\t\t};\n\t\t\t\n\t\t\tfetch(`/api/projects/${projectId}/config`, {\n\t\t\t\tmethod: 'POST',\n\t\t\t\theaders: {\n\t\t\t\t\t'Content-Type': 'application/json',\n\t\t\t\t},\n\t\t\t\tbody: JSON.stringify(config)\n\t\t\t})\n\t\t\t.then(response => {\n\t\t\t\tif (response.ok) {\n\t\t\t\t\talert('配置保存成功');\n\t\t\t\t\tcloseConfigModal();\n\t\t\t\t\tlocation.reload();\n\t\t\t\t} else {\n\t\t\t\t\talert('配置保存失败');\n\t\t\t\t}\n\t\t\t})\n\t\t\t.catch(error => {\n\t\t\t\tconsole.error('Error:', error);\n\t\t\t\talert('配置保存失败');\n\t\t\t});\n\t\t}\n\t\t\n\t\tfunction testConfig() {\n\t\t\tconst projectId = document.getElementById('config-project-id').value;\n\t\t\tfetch(`/api/projects/${projectId}/test-config`, {\n\t\t\t\tmethod: 'POST'\n\t\t\t})\n\t\t\t.then(response => response.json())\n\t\t\t.then(data => {\n\t\t\t\tif (data.success) {\n\t\t\t\t\talert('配置测试通过');\n\t\t\t\t} else {\n\t\t\t\t\talert('配置测试失败: ' + data.error);\n\t\t\t\t}\n\t\t\t})\n\t\t\t.catch(error => {\n\t\t\t\tconsole.error('Error:', error);\n\t\t\t\talert('配置测试失败');\n\t\t\t});\n\t\t}\n\t\t\n\t\tfunction reloadNginx() {\n\t\t\tfetch('/api/nginx/reload', {\n\t\t\t\tmethod: 'POST'\n\t\t\t})\n\t\t\t.then(response => {\n\t\t\t\tif (response.ok) {\n\t\t\t\t\talert('Nginx重载成功');\n\t\t\t\t} else {\n\t\t\t\t\talert('Nginx重载失败');\n\t\t\t\t}\n\t\t\t})\n\t\t\t.catch(error => {\n\t\t\t\tconsole.error('Error:', error);\n\t\t\t\talert('Nginx重载失败');\n\t\t\t});\n\t\t}\n\t\t\n\t\tfunction updateNginxPreview() {\n\t\t\tconst domain = document.getElementById('config-domain').value || 'example.com';\n\t\t\tconst aliases = document.getElementById('config-aliases').value.split('\\n').filter(a => a.trim());\n\t\t\tconst documentRoot = document.getElementById('config-document-root').value || '/var/www/html';\n\t\t\tconst sslEnabled = document.getElementById('config-ssl-enabled').checked;\n\t\t\t\n\t\t\t// Show/hide SSL method section\n\t\t\tconst sslMethodSection = document.getElementById('ssl-method-section');\n\t\t\tif (sslEnabled) {\n\t\t\t\tsslMethodSection.style.display = 'block';\n\t\t\t} else {\n\t\t\t\tsslMethodSection.style.display = 'none';\n\t\t\t}\n\t\t\tconst phpEnabled = document.getElementById('config-php-enabled').checked;\n\t\t\tconst phpVersion = document.getElementById('config-php-version').value;\n\t\t\tconst indexFiles = document.getElementById('config-index-files').value || 'index.html index.php';\n\t\t\tconst customNginx = document.getElementById('config-custom-nginx').value;\n\t\t\t\n\t\t\tlet config = `server {\\n`;\n\t\t\tconfig += `    listen 80;\\n`;\n\t\t\tconfig += `    server_name ${domain}`;\n\t\t\tif (aliases.length > 0) {\n\t\t\t\tconfig += ` ${aliases.join(' ')}`;\n\t\t\t}\n\t\t\tconfig += `;\\n`;\n\t\t\tconfig += `    root ${documentRoot};\\n`;\n\t\t\tconfig += `    index ${indexFiles};\\n\\n`;\n\t\t\t\n\t\t\tif (sslEnabled) {\n\t\t\t\tconfig += `    # Redirect HTTP to HTTPS\\n`;\n\t\t\t\tconfig += `    return 301 https://$server_name$request_uri;\\n`;\n\t\t\t\tconfig += `}\\n\\n`;\n\t\t\t\tconfig += `server {\\n`;\n\t\t\t\tconfig += `    listen 443 ssl http2;\\n`;\n\t\t\t\tconfig += `    server_name ${domain}`;\n\t\t\t\tif (aliases.length > 0) {\n\t\t\t\t\tconfig += ` ${aliases.join(' ')}`;\n\t\t\t\t}\n\t\t\t\tconfig += `;\\n`;\n\t\t\t\tconfig += `    root ${documentRoot};\\n`;\n\t\t\t\tconfig += `    index ${indexFiles};\\n\\n`;\n\t\t\t\tconfig += `    ssl_certificate /etc/ssl/certs/${domain}.crt;\\n`;\n\t\t\t\tconfig += `    ssl_certificate_key /etc/ssl/private/${domain}.key;\\n\\n`;\n\t\t\t}\n\t\t\t\n\t\t\tif (phpEnabled) {\n\t\t\t\tconfig += `    location ~ \\\\.php$ {\\n`;\n\t\t\t\tconfig += `        try_files $uri =404;\\n`;\n\t\t\t\tconfig += `        fastcgi_split_path_info ^(.+\\\\.php)(/.+)$;\\n`;\n\t\t\t\tconfig += `        fastcgi_pass unix:/var/run/php/php${phpVersion}-fpm.sock;\\n`;\n\t\t\t\tconfig += `        fastcgi_index index.php;\\n`;\n\t\t\t\tconfig += `        include fastcgi_params;\\n`;\n\t\t\t\tconfig += `        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;\\n`;\n\t\t\t\tconfig += `    }\\n\\n`;\n\t\t\t}\n\t\t\t\n\t\t\tconfig += `    location / {\\n`;\n\t\t\tconfig += `        try_files $uri $uri/ =404;\\n`;\n\t\t\tconfig += `    }\\n`;\n\t\t\t\n\t\t\tif (customNginx.trim()) {\n\t\t\t\tconfig += `\\n    # Custom Configuration\\n`;\n\t\t\t\tconfig += `    ${customNginx.replace(/\\n/g, '\\n    ')}\\n`;\n\t\t\t}\n\t\t\t\n\t\t\tconfig += `}`;\n\t\t\t\n\t\t\tdocument.getElementById('nginx-preview').textContent = config;\n\t\t}\n\t</script>")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		return nil
	})
}

var _ = templruntime.GeneratedTemplate
