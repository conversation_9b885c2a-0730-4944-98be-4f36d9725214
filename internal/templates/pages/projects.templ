package pages

import (
	"server-panel/internal/projects"
	"server-panel/internal/templates/layouts"
	"server-panel/internal/templates/components"
	"server-panel/internal/i18n"
	"strconv"
	"fmt"
)

func formatBytesSimple(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

templ Projects(title, username, currentLang string, overview *projects.ProjectOverview) {
	@layouts.Base(title, username, currentLang) {
		<div class="min-h-screen bg-gray-50 dark:bg-gray-900" x-data="projectManager()">
			<div class="relative z-10">
				<!-- Welcome Banner for First Time Setup -->
				if overview.FirstTimeSetup {
					@components.NotificationBanner(
						"welcome",
						i18n.T(currentLang, "projects.welcome.title"),
						i18n.T(currentLang, "projects.welcome.message"),
						[]components.BannerAction{
							{Text: i18n.T(currentLang, "projects.create.first"), OnClick: "showCreateModal = true", Style: "primary"},
							{Text: i18n.T(currentLang, "projects.skip.now"), OnClick: "", Style: "secondary"},
						},
						true,
					)
				}

		</div>

			<!-- Create Project Modal -->
			<div x-show="showCreateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 dark:bg-gray-900 dark:bg-opacity-75 overflow-y-auto h-full w-full z-50">
				<div class="relative top-20 mx-auto p-5 border border-gray-200 dark:border-gray-700 w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
					<div class="mt-3">
						<h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">{ i18n.T(currentLang, "projects.create_title") }</h3>
						<form @submit.prevent="createProject()">
							<div class="mb-4">
								<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{ i18n.T(currentLang, "projects.project_name") }</label>
								<input
									type="text"
									x-model="newProject.name"
									class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
									placeholder="my-awesome-project"
									required>
								<p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
									<svg class="inline w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z"></path>
									</svg>
									项目将创建在: <span class="font-mono text-blue-600 dark:text-blue-400" x-text="projectsDir + '/' + (newProject.name || '[项目名称]')"></span>
								</p>
							</div>
							<div class="mb-4">
								<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{ i18n.T(currentLang, "projects.domain") }</label>
								<input
									type="text"
									x-model="newProject.domain"
									class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
									:placeholder="'example.com 或留空使用 ' + serverInfo.default_domain_format">
								<p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
									<svg class="inline w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
									</svg>
									留空将自动使用: <span class="font-mono text-blue-600 dark:text-blue-400" x-text="serverInfo.default_domain_format"></span>
								</p>
							</div>
							<div class="mb-4 space-y-2">
								<label class="flex items-center">
									<input type="checkbox" x-model="newProject.createDB" class="mr-2 text-blue-600 dark:text-blue-400">
									<span class="text-sm text-gray-700 dark:text-gray-300">{ i18n.T(currentLang, "projects.create_database") }</span>
								</label>
								<label class="flex items-center">
									<input type="checkbox" x-model="newProject.enableSSL" class="mr-2 text-blue-600 dark:text-blue-400">
									<span class="text-sm text-gray-700 dark:text-gray-300">{ i18n.T(currentLang, "projects.enable_ssl") }</span>
								</label>
								<label class="flex items-center">
									<input type="checkbox" x-model="newProject.enableBackup" class="mr-2 text-blue-600 dark:text-blue-400">
									<span class="text-sm text-gray-700 dark:text-gray-300">{ i18n.T(currentLang, "projects.enable_backup") }</span>
								</label>
							</div>
							<div class="flex space-x-3">
								<button
									type="submit"
									class="flex-1 bg-blue-500 dark:bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-300">
									{ i18n.T(currentLang, "projects.create_project") }
								</button>
								<button
									type="button"
									@click="showCreateModal = false"
									class="flex-1 bg-gray-300 dark:bg-gray-600 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md hover:bg-gray-400 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-300">
									{ i18n.T(currentLang, "common.cancel") }
								</button>
							</div>
						</form>
					</div>
				</div>
			</div>

			<!-- Main Content -->
			<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
				<!-- Page Header -->
				if len(overview.Projects) > 0 {
					<div class="flex justify-between items-center mb-8">
						<div>
							<h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">{ i18n.T(currentLang, "projects.title") }</h1>
							<p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{ i18n.T(currentLang, "projects.subtitle") }</p>
						</div>
						<button
							@click="showCreateModal = true"
							class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
							<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
							</svg>
							{ i18n.T(currentLang, "projects.new_project") }
						</button>
					</div>
				}
				
				<!-- Stats Cards -->
				<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
					<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
						<div class="flex items-center">
							<div class="flex-shrink-0">
								<div class="w-8 h-8 bg-blue-500 dark:bg-blue-600 rounded-md flex items-center justify-center">
									<svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
										<path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
									</svg>
								</div>
							</div>
							<div class="ml-5 w-0 flex-1">
								<dl>
									<dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">{ i18n.T(currentLang, "projects.total") }</dt>
									<dd class="text-lg font-medium text-gray-900 dark:text-gray-100">{ strconv.Itoa(overview.TotalProjects) }</dd>
								</dl>
							</div>
						</div>
					</div>

					<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
						<div class="flex items-center">
							<div class="flex-shrink-0">
								<div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
									<svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
									</svg>
								</div>
							</div>
							<div class="ml-5 w-0 flex-1">
								<dl>
									<dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">{ i18n.T(currentLang, "projects.active") }</dt>
									<dd class="text-lg font-medium text-gray-900 dark:text-gray-100">{ strconv.Itoa(overview.ActiveProjects) }</dd>
								</dl>
							</div>
						</div>
					</div>

					<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
						<div class="flex items-center">
							<div class="flex-shrink-0">
								<div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
									<svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
										<path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
									</svg>
								</div>
							</div>
							<div class="ml-5 w-0 flex-1">
								<dl>
									<dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">{ i18n.T(currentLang, "projects.total_size") }</dt>
									<dd class="text-lg font-medium text-gray-900 dark:text-gray-100">{ formatBytesSimple(overview.TotalSize) }</dd>
								</dl>
							</div>
						</div>
					</div>

					<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
						<div class="flex items-center">
							<div class="flex-shrink-0">
								<div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
									<svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 011 1v1a1 1 0 01-1 1H4a1 1 0 01-1-1v-1zM3 7a1 1 0 011-1h12a1 1 0 011 1v1a1 1 0 01-1 1H4a1 1 0 01-1-1V7zM3 12a1 1 0 011-1h12a1 1 0 011 1v1a1 1 0 01-1 1H4a1 1 0 01-1-1v-1z" clip-rule="evenodd"></path>
									</svg>
								</div>
							</div>
							<div class="ml-5 w-0 flex-1">
								<dl>
									<dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">{ i18n.T(currentLang, "projects.backups") }</dt>
									<dd class="text-lg font-medium text-gray-900 dark:text-gray-100">0</dd>
								</dl>
							</div>
						</div>
					</div>
				</div>

				<!-- Project Cards -->
				if len(overview.Projects) > 0 {
					<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
						for _, project := range overview.Projects {
							@components.ProjectCard(project)
						}
						<!-- Add Project Card -->
						<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border-2 border-dashed border-gray-300 dark:border-gray-600 p-6 hover:shadow-md hover:border-blue-400 dark:hover:border-blue-500 transition-all cursor-pointer group"
							 @click="showCreateModal = true">
							<div class="flex flex-col items-center justify-center h-full min-h-[200px] text-center">
								<div class="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mb-4 group-hover:bg-blue-200 dark:group-hover:bg-blue-800 transition-colors">
									<svg class="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
									</svg>
								</div>
								<h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">{ i18n.T(currentLang, "projects.add.title") }</h3>
								<p class="text-sm text-gray-500 dark:text-gray-400 max-w-xs">{ i18n.T(currentLang, "projects.add.description") }</p>
							</div>
						</div>
					</div>
				} else {
					<div class="text-center py-12">
						<svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
						</svg>
						<h3 class="mt-2 text-sm font-medium text-gray-900">{ i18n.T(currentLang, "projects.no_projects") }</h3>
						<p class="mt-1 text-sm text-gray-500">{ i18n.T(currentLang, "projects.get_started") }</p>
						<div class="mt-6">
							<button
								@click="showCreateModal = true"
								class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
								<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
								</svg>
								{ i18n.T(currentLang, "projects.new_project") }
							</button>
						</div>
					</div>
				}
			</div>
		</div>

		<!-- Project Configuration Modal -->
		@components.ProjectConfigModal(currentLang)

		<!-- Alpine.js Project Manager -->
		<script>
			function projectManager() {
				return {
					showCreateModal: false,
					projectsDir: '/var/www', // 默认值，将通过API更新
					serverInfo: {
						host: 'localhost:9090',
						server_ip: 'localhost',
						default_domain_format: 'localhost:9090'
					},
					newProject: {
						name: '',
						domain: '',
						createDB: true,
						enableSSL: false,
						enableBackup: false
					},

					init() {
						// 获取项目配置信息
						this.loadProjectsConfig();
					},

					loadProjectsConfig() {
						fetch('/api/projects/config')
							.then(response => response.json())
							.then(data => {
								if (data.success) {
									this.projectsDir = data.data.projects_dir;
									this.serverInfo = data.data.server_info;
								}
							})
							.catch(error => {
								console.error('Failed to load projects config:', error);
							});
					},

					refreshProjects() {
						window.location.reload();
					},

					createProject() {
						if (!this.newProject.name) {
							alert('Project name is required');
							return;
						}

						fetch('/api/projects/create', {
							method: 'POST',
							headers: {
								'Content-Type': 'application/json',
							},
							body: JSON.stringify(this.newProject)
						})
						.then(response => response.json())
						.then(data => {
							if (data.success) {
								this.showCreateModal = false;
								this.refreshProjects();
							} else {
								alert('Failed to create project: ' + data.error);
							}
						})
						.catch(error => {
							alert('Failed to create project: ' + error.message);
						});
					}
				}
			}
		</script>
	}
}
