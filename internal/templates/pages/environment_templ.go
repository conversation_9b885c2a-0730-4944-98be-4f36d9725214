// Code generated by templ - DO NOT EDIT.

// templ: version: v0.3.924
package pages

//lint:file-ignore SA4006 This context is only used if a nested component is present.

import "github.com/a-h/templ"
import templruntime "github.com/a-h/templ/runtime"

import (
	"server-panel/internal/environment"
	"server-panel/internal/i18n"
	"server-panel/internal/templates/components"
	"server-panel/internal/templates/layouts"
)

func Environment(title, username, currentLang string, overview *environment.EnvironmentOverview) templ.Component {
	return templruntime.GeneratedTemplate(func(templ_7745c5c3_Input templruntime.GeneratedComponentInput) (templ_7745c5c3_Err error) {
		templ_7745c5c3_W, ctx := templ_7745c5c3_Input.Writer, templ_7745c5c3_Input.Context
		if templ_7745c5c3_CtxErr := ctx.Err(); templ_7745c5c3_CtxErr != nil {
			return templ_7745c5c3_CtxErr
		}
		templ_7745c5c3_Buffer, templ_7745c5c3_IsBuffer := templruntime.GetBuffer(templ_7745c5c3_W)
		if !templ_7745c5c3_IsBuffer {
			defer func() {
				templ_7745c5c3_BufErr := templruntime.ReleaseBuffer(templ_7745c5c3_Buffer)
				if templ_7745c5c3_Err == nil {
					templ_7745c5c3_Err = templ_7745c5c3_BufErr
				}
			}()
		}
		ctx = templ.InitializeContext(ctx)
		templ_7745c5c3_Var1 := templ.GetChildren(ctx)
		if templ_7745c5c3_Var1 == nil {
			templ_7745c5c3_Var1 = templ.NopComponent
		}
		ctx = templ.ClearChildren(ctx)
		templ_7745c5c3_Var2 := templruntime.GeneratedTemplate(func(templ_7745c5c3_Input templruntime.GeneratedComponentInput) (templ_7745c5c3_Err error) {
			templ_7745c5c3_W, ctx := templ_7745c5c3_Input.Writer, templ_7745c5c3_Input.Context
			templ_7745c5c3_Buffer, templ_7745c5c3_IsBuffer := templruntime.GetBuffer(templ_7745c5c3_W)
			if !templ_7745c5c3_IsBuffer {
				defer func() {
					templ_7745c5c3_BufErr := templruntime.ReleaseBuffer(templ_7745c5c3_Buffer)
					if templ_7745c5c3_Err == nil {
						templ_7745c5c3_Err = templ_7745c5c3_BufErr
					}
				}()
			}
			ctx = templ.InitializeContext(ctx)
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 1, "<div class=\"min-h-screen bg-gray-50 dark:bg-gray-900\" x-data=\"environmentManager()\"><!-- Welcome Banner for First Time Setup -->")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			if overview.FirstTimeSetup {
				templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 2, "<div class=\"mb-4\">")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				templ_7745c5c3_Err = components.NotificationBanner(
					"welcome",
					i18n.T(currentLang, "environment.welcome.title"),
					i18n.T(currentLang, "environment.welcome.message"),
					[]components.BannerAction{
						{Text: i18n.T(currentLang, "environment.install.all"), OnClick: "bulkInstall()", Style: "primary"},
						{Text: i18n.T(currentLang, "environment.skip.now"), OnClick: "", Style: "secondary"},
					},
					true,
				).Render(ctx, templ_7745c5c3_Buffer)
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 3, "</div><!-- Recommended Setup List --> ")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				if len(overview.RecommendedSetup) > 0 {
					templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 4, "<div class=\"bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-400 dark:border-blue-500 p-4 mb-6\"><div class=\"flex\"><div class=\"flex-shrink-0\"><svg class=\"h-5 w-5 text-blue-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\"><path fill-rule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clip-rule=\"evenodd\"></path></svg></div><div class=\"ml-3\"><h3 class=\"text-sm font-medium text-blue-800 dark:text-blue-200\">Recommended Environment Stack</h3><div class=\"mt-2 text-sm text-blue-700 dark:text-blue-300\"><div class=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2\">")
					if templ_7745c5c3_Err != nil {
						return templ_7745c5c3_Err
					}
					for _, service := range overview.RecommendedSetup {
						templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 5, "<div class=\"flex items-center\"><svg class=\"w-4 h-4 mr-2 text-green-500\" fill=\"currentColor\" viewBox=\"0 0 20 20\"><path fill-rule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clip-rule=\"evenodd\"></path></svg> <span class=\"text-sm\">")
						if templ_7745c5c3_Err != nil {
							return templ_7745c5c3_Err
						}
						var templ_7745c5c3_Var3 string
						templ_7745c5c3_Var3, templ_7745c5c3_Err = templ.JoinStringErrs(service)
						if templ_7745c5c3_Err != nil {
							return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/pages/environment.templ`, Line: 46, Col: 43}
						}
						_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var3))
						if templ_7745c5c3_Err != nil {
							return templ_7745c5c3_Err
						}
						templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 6, "</span></div>")
						if templ_7745c5c3_Err != nil {
							return templ_7745c5c3_Err
						}
					}
					templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 7, "</div></div></div></div></div>")
					if templ_7745c5c3_Err != nil {
						return templ_7745c5c3_Err
					}
				}
			}
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 8, "<!-- Main Content --><div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\"><!-- Progress Bar (hidden by default) --><div x-show=\"showProgress\" class=\"mb-6\"><div class=\"bg-white rounded-lg shadow p-6\"><div class=\"flex items-center justify-between mb-2\"><h3 class=\"text-lg font-medium text-gray-900\" x-text=\"progressTitle\">Installing...</h3><span class=\"text-sm text-gray-500\" x-text=\"progressPercent + '%'\">0%</span></div><div class=\"w-full bg-gray-200 rounded-full h-2\"><div class=\"bg-blue-600 h-2 rounded-full transition-all duration-300\" :style=\"'width: ' + progressPercent + '%'\"></div></div><p class=\"mt-2 text-sm text-gray-600\" x-text=\"progressMessage\">Preparing...</p></div></div><!-- Environment Cards --><div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			for _, service := range overview.Services {
				templ_7745c5c3_Err = components.EnvironmentCard(service, currentLang).Render(ctx, templ_7745c5c3_Buffer)
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
			}
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 9, "</div><!-- Settings Modal --><div x-show=\"showSettingsModal\" @keydown.escape.window=\"showSettingsModal = false\" class=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 transition-opacity duration-300\" style=\"display: none;\"><div @click.away=\"showSettingsModal = false\" class=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[80vh] flex flex-col\"><div id=\"settings-section\" class=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 transition-all duration-300\"><div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center\"><div><h3 class=\"text-lg font-medium text-gray-900 dark:text-gray-100\" x-text=\"currentService.charAt(0).toUpperCase() + currentService.slice(1) + ' Settings'\"></h3><p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\" x-text=\"'Manage ' + currentService + ' settings and extensions'\"></p></div><button @click=\"showSettingsModal = false\" class=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none\"><svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path></svg></button></div><div class=\"p-6 overflow-y-auto\"><div x-show=\"currentService === 'php'\">")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			if len(overview.PHPExtensions) > 0 {
				templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 10, "<div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				for _, ext := range overview.PHPExtensions {
					templ_7745c5c3_Err = components.PHPExtensionCard(ext).Render(ctx, templ_7745c5c3_Buffer)
					if templ_7745c5c3_Err != nil {
						return templ_7745c5c3_Err
					}
				}
				templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 11, "</div>")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
			} else {
				templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 12, "<p class=\"text-gray-500 dark:text-gray-400\">No PHP extensions available.</p>")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
			}
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 13, "</div><div x-show=\"currentService !== 'php'\"><p class=\"text-gray-500 dark:text-gray-400\">Settings for this service are not yet available.</p></div></div></div></div></div></div></div><!-- Alpine.js Environment Manager --> <script>\n\t\t\tfunction environmentManager() {\n\t\t\t\treturn {\n\t\t\t\t\tshowSettingsModal: false,\n\t\t\t\t\tcurrentService: '',\n\t\t\t\t\topenSettingsModal(service) {\n\t\t\t\t\t\tthis.currentService = service;\n\t\t\t\t\t\tthis.showSettingsModal = true;\n\t\t\t\t\t},\n\n\t\t\t\t\tstartService(serviceName) {\n\t\t\t\t\t\tfetch(`/api/environment/start`, {\n\t\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\t\theaders: { 'Content-Type': 'application/json' },\n\t\t\t\t\t\t\tbody: JSON.stringify({ name: serviceName })\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.then(res => res.json())\n\t\t\t\t\t\t.then(data => {\n\t\t\t\t\t\t\tif (data.success) {\n\t\t\t\t\t\t\t\tshowNotification('Service started successfully', 'success');\n\t\t\t\t\t\t\t\tlocation.reload();\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tshowNotification('Failed to start service: ' + data.error, 'error');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.catch(function(err) { showNotification('Error starting service: ' + err.message, 'error'); });\n\t\t\t\t\t},\n\n\t\t\t\t\tstopService(serviceName) {\n\t\t\t\t\t\tshowConfirmModal(\n\t\t\t\t\t\t\t'Confirm Stop Service',\n\t\t\t\t\t\t\t`Are you sure you want to stop ${serviceName}?`,\n\t\t\t\t\t\t\t'Stop',\n\t\t\t\t\t\t\t() => {\n\t\t\t\t\t\t\t\tfetch(`/api/environment/stop`, {\n\t\t\t\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\t\t\t\theaders: { 'Content-Type': 'application/json' },\n\t\t\t\t\t\t\t\t\tbody: JSON.stringify({ name: serviceName })\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t.then(res => res.json())\n\t\t\t\t\t\t\t\t.then(data => {\n\t\t\t\t\t\t\t\t\tif (data.success) {\n\t\t\t\t\t\t\t\t\t\tshowNotification('Service stopped successfully', 'success');\n\t\t\t\t\t\t\t\t\t\tlocation.reload();\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\tshowNotification('Failed to stop service: ' + data.error, 'error');\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t.catch(err => showNotification('Error stopping service: ' + err.message, 'error'));\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t);\n\t\t\t\t\t},\n\n\t\t\t\t\trestartService(serviceName) {\n\t\t\t\t\t\tfetch(`/api/environment/restart`, {\n\t\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\t\theaders: { 'Content-Type': 'application/json' },\n\t\t\t\t\t\t\tbody: JSON.stringify({ name: serviceName })\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.then(res => res.json())\n\t\t\t\t\t\t.then(data => {\n\t\t\t\t\t\t\tif (data.success) {\n\t\t\t\t\t\t\t\tshowNotification('Service restarted successfully', 'success');\n\t\t\t\t\t\t\t\tlocation.reload();\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tshowNotification('Failed to restart service: ' + data.error, 'error');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.catch(err => showNotification('Error restarting service: ' + err.message, 'error'));\n\t\t\t\t\t},\n\n\t\t\t\t\tuninstallService(serviceName) {\n\t\t\t\t\t\tshowConfirmModal(\n\t\t\t\t\t\t\t'Confirm Uninstall',\n\t\t\t\t\t\t\t`Are you sure you want to uninstall ${serviceName}? This action cannot be undone.`,\n\t\t\t\t\t\t\t'Uninstall',\n\t\t\t\t\t\t\t() => {\n\t\t\t\t\t\t\t\tfetch(`/api/environment/uninstall`, {\n\t\t\t\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\t\t\t\theaders: { 'Content-Type': 'application/json' },\n\t\t\t\t\t\t\t\t\tbody: JSON.stringify({ name: serviceName })\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t.then(res => res.json())\n\t\t\t\t\t\t\t\t.then(data => {\n\t\t\t\t\t\t\t\t\tif (data.success) {\n\t\t\t\t\t\t\t\t\t\tshowNotification('Service uninstalled successfully', 'success');\n\t\t\t\t\t\t\t\t\t\tlocation.reload();\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\tshowNotification('Failed to uninstall service: ' + data.error, 'error');\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t.catch(err => showNotification('Error uninstalling service: ' + err.message, 'error'));\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t);\n\t\t\t\t\t},\n\t\t\t\t\tshowProgress: false,\n\t\t\t\t\tprogressTitle: '',\n\t\t\t\t\tprogressPercent: 0,\n\t\t\t\t\tprogressMessage: '',\n\n\t\t\t\t\trefreshEnvironment() {\n\t\t\t\t\t\twindow.location.reload();\n\t\t\t\t\t},\n\n\t\t\t\t\tbulkInstall() {\n\t\t\t\t\t\tthis.showProgress = true;\n\t\t\t\t\t\tthis.progressTitle = 'Installing Environment Stack';\n\t\t\t\t\t\tthis.progressPercent = 0;\n\t\t\t\t\t\tthis.progressMessage = 'Preparing installation...';\n\n\t\t\t\t\t\t// Start bulk installation\n\t\t\t\t\t\tfetch('/api/environment/bulk-install', {\n\t\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\t\theaders: {\n\t\t\t\t\t\t\t\t'Content-Type': 'application/json',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tbody: JSON.stringify({\n\t\t\t\t\t\t\t\tservices: ['nginx', 'php', 'mariadb', 'redis'],\n\t\t\t\t\t\t\t\tconfirm: true\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.then(response => response.json())\n\t\t\t\t\t\t.then(data => {\n\t\t\t\t\t\t\tif (data.success) {\n\t\t\t\t\t\t\t\tthis.trackProgress();\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis.showError('Installation failed: ' + data.error);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.catch(error => {\n\t\t\t\t\t\t\tthis.showError('Installation failed: ' + error.message);\n\t\t\t\t\t\t});\n\t\t\t\t\t},\n\n\t\t\t\t\ttrackProgress() {\n\t\t\t\t\t\t// Poll for progress updates\n\t\t\t\t\t\tconst interval = setInterval(() => {\n\t\t\t\t\t\t\tfetch('/api/environment/progress')\n\t\t\t\t\t\t\t\t.then(response => response.json())\n\t\t\t\t\t\t\t\t.then(data => {\n\t\t\t\t\t\t\t\t\tif (data.success && data.data) {\n\t\t\t\t\t\t\t\t\t\tthis.progressPercent = data.data.progress;\n\t\t\t\t\t\t\t\t\t\tthis.progressMessage = data.data.message;\n\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\tif (data.data.status === 'completed') {\n\t\t\t\t\t\t\t\t\t\t\tclearInterval(interval);\n\t\t\t\t\t\t\t\t\t\t\tthis.showProgress = false;\n\t\t\t\t\t\t\t\t\t\t\tthis.refreshEnvironment();\n\t\t\t\t\t\t\t\t\t\t} else if (data.data.status === 'error') {\n\t\t\t\t\t\t\t\t\t\t\tclearInterval(interval);\n\t\t\t\t\t\t\t\t\t\t\tthis.showError(data.data.message);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t.catch(error => {\n\t\t\t\t\t\t\t\t\tclearInterval(interval);\n\t\t\t\t\t\t\t\t\tthis.showError('Failed to track progress: ' + error.message);\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t},\n\n\t\t\t\t\tshowError(message) {\n\t\t\t\t\t\tthis.showProgress = false;\n\t\t\t\t\t\talert('Error: ' + message);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t</script> <!-- 自定义确认弹窗 --> ")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			templ_7745c5c3_Err = components.ConfirmModal().Render(ctx, templ_7745c5c3_Buffer)
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 14, " <!-- 通知系统 --> ")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			templ_7745c5c3_Err = components.Notification().Render(ctx, templ_7745c5c3_Buffer)
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			return nil
		})
		templ_7745c5c3_Err = layouts.Base(title, username, currentLang).Render(templ.WithChildren(ctx, templ_7745c5c3_Var2), templ_7745c5c3_Buffer)
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		return nil
	})
}

var _ = templruntime.GeneratedTemplate
